import axios from 'axios'
import router from '@/router'
import socket from './socket.js'

// let _this = new Vue();
let isLock = false
let refreshSubscribers = []
//设置请求超时
axios.defaults.timeout = 30001
const VUE_APP_BASE_API = process.env.VUE_APP_BASE_API

// 存储每个请求的AbortController实例
const abortControllers = new Map();

const request = (url, method, data) => {
  return new Promise((resolve, reject) => {
    const abortController = new AbortController();
    abortControllers.set(url, abortController);

    baseRequest(url, method, data, resolve, reject, abortController.signal)

  });
};
// 新增取消请求的方法
const cancelRequest = (url) => {

  const abortController = abortControllers.get(url);
  if (abortController) {
    abortController.abort();
  }
};

const baseRequest = (url, method, data, resolve, reject, signal) => {

  const token =
    getToken() && JSON.parse(getToken()).access_token
      ? JSON.parse(getToken()).access_token
      : null
  const headers = token
    ? { 'content-type': url == "/file/upload" ? 'multipart/form-data' : 'application/json', Authorization: token }
    : {}

  var axData = {}
  var axParam = {}
  if (method === 'GET') {
    axData = {}
    axParam = data
  } else {
    axData = data
    axParam = {}
  }

  axios({
    method: method,
    url: VUE_APP_BASE_API + url,
    data: axData,
    params: axParam,
    timeout: 1000 * 60,
    headers: headers,
    signal: signal // 将signal传递给axios请求
  })
    .then(function (res) {

      let code = res.data.code

      switch (code) {
        case 0:
          resolve(res.data)
          break
        case 200:
          // other handlers
          break
        case 503:
        case 500:
          resolve(res && res.data ? res.data : res)
          break

        case 401:
        case 402:
          console.log('接口返回401/402，token过期！')
          reLogin()
          break

        default:
          reject(res)
      }
    })
    .catch(function (err) {

      if (err.response !== null && err.response !== undefined) {
        if (err.response.status === 401 || err.response.status === 402) {
          console.log('请求返回401/402，token过期！')
          reLogin() // 直接调用重新登录
          reject(err)
        } else {
          if (err.response.status !== 200) {
            console.log('服务器错误')
            reject(err)
          } else reject(err)
        }
      }
    })
    .finally(function (e) {
      // 请求结束后从map中移除对应的AbortController
      abortControllers.delete(url);
      reject(e)
    })
}

function reLogin() {
  socket.closeSocket()

  window.$lcStore.removeAll()

  router.replace({
    path: '/login'
  })
}

const requestSubscribe = (url, method, data) => {
  return new Promise((resolve, reject) => {
    baseRequestSubscribe(url, method, data, resolve, reject)
  })
}

const baseRequestSubscribe = (url, method, data, resolve, reject) => {
  const token =
    getToken() && JSON.parse(getToken()).access_token
      ? JSON.parse(getToken()).access_token
      : null

  const headers = token
    ? { 'content-type': 'application/json', Authorization: token }
    : {}

  var axData = {}
  var axParam = {}
  if (method === 'GET') {
    axData = {}
    axParam = data
  } else {
    axData = data
    axParam = {}
  }
  axios({
    method: method,
    url: url,
    data: axData,
    params: axParam,
    timeout: 1000 * 60,
    headers: headers
  })
    .then(function (res) {

      let code = res.data.code
      switch (code) {
        case 0:
          resolve(res.data)
          break
        case 200:
          // other handlers
          break
        case 503:
        case 500:
          resolve(res && res.data ? res.data : res)
          break
        case 401:
        case 402:
          console.log('接口返回401/402，token过期！')
          reLogin()
          break
        default:

          reject(res)
      }
    })
    .catch(function (err) {
      if (err.response !== null && err.response !== undefined) {
        if (err.response.status === 401 || err.response.status === 402) {
          console.log('请求返回401/402，token过期！')
          reLogin() // 直接调用重新登录
          reject(err)
        } else {
          if (err.response.status !== 200) {
            console.log('服务器错误')
            reject(err)
          } else {
            reject(err)
          }
        }
      }
    })
    .finally(function (e) {

      reject(e)
    })
}

//判断token是否过期
function isTokenExpired(token) {
  try {
    let tokenObj = JSON.parse(token);
    // 检查token对象是否有效
    if (!tokenObj || !tokenObj.expires_time) {
      console.log('Token格式无效或缺少过期时间');
      return true;
    }

    let expires_time = tokenObj.expires_time;
    let currentTime = new Date().getTime();

    // 提前5分钟(300000毫秒)判断过期，避免即将过期的情况
    if (currentTime >= (expires_time - 300000)) {
      console.log('Token已过期或即将过期');
      return true;
    } else {
      return false;
    }
  } catch (error) {
    console.error('解析token时出错:', error);
    return true; // 解析出错时视为过期
  }
}
//获取Token对象
function getToken() {
  return window.$lcStore.wdcyToken
}
//push所有请求到数组中
function subscribeTokenRefresh(cb) {
  refreshSubscribers.push(cb)
}

//刷新请求（refreshSubscribers数组中的请求得到新的token之后会自执行，用新的token去请求数据）
function onRrefreshed(token) {
  refreshSubscribers.map((cb) => cb(token))
}

//刷新token
function refreshToken(config, token, resolve, reject) {
  try {
    let tokenObj = JSON.parse(token);
    if (!tokenObj || !tokenObj.refresh_token) {
      console.error('刷新token失败: refresh_token不存在');
      reLogin();
      return;
    }

    let data = {
      grantType: 'refresh',
      refreshToken: tokenObj.refresh_token
    }

    axios({
      method: 'post',
      url: VUE_APP_BASE_API + '/auth/token', //刷新token的接口
      headers: {
        'content-type': 'application/json'
      },
      data: data
    })
      .then((res) => {
        isLock = false //释放锁
        if (!res.data || res.data.code === -1 || !res.data.result) {
          console.error('刷新token失败: 接口返回错误', res.data);
          reLogin();
          return;
        }

        try {
          let expires_time =
            new Date().getTime() + parseInt(res.data.result.expires_in * 0.8) * 1000;
          let currentToken = JSON.parse(getToken()) || {};

          currentToken.expires_time = expires_time;
          currentToken.access_token = res.data.result.access_token;
          currentToken.refresh_token = res.data.result.refresh_token;

          window.$lcStore.setWdcyToken(JSON.stringify(currentToken));

          config.headers.Authorization = res.data.result.access_token;
          resolve(config);

          //执行数组里的函数,重新发起被挂起的请求
          onRrefreshed(res.data.result.access_token);
          //清空数组中保存的请求
          refreshSubscribers = [];
        } catch (error) {
          console.error('保存新token时出错:', error);
          reLogin();
        }
      })
      .catch((err) => {
        console.error('刷新token请求失败:', err);
        isLock = false; // 释放锁
        reLogin();
        if (reject) reject(err);
      });
  } catch (error) {
    console.error('刷新token过程中出错:', error);
    isLock = false; // 释放锁
    reLogin();
  }
}

axios.interceptors.request.use(
  (config) => {
    let token = getToken()

    if (token) {
      //判断token是否过期,如果过期请求刷新token
      if (isTokenExpired(token)) {
        //判断当前是否正在请求刷新token
        if (!isLock) {
          isLock = true //isLock设置true,锁住防止死循环。
          //使用Promise等待刷新完成返回配置信息
          let refresh = new Promise((resolve, reject) => {
            refreshToken(config, token, resolve, reject)
          })

          return refresh
        } else {
          //判断当前url是否是刷新token的请求地址，如果是直接下一步。
          if (config.url.indexOf('auth') == -1) {
            //把请求(token)=>{....}都push到一个数组中
            let retry = new Promise((resolve) => {
              //(token) => {...}这个函数就是回调函数
              subscribeTokenRefresh((token) => {
                config.headers.Authorization = token
                //将请求挂起
                resolve(config)
              })
            })
            return retry
          } else {
            return config
          }
        }
      } else {
        return config
      }
    } else {
      return config
    }
  },
  (error) => {
    return Promise.reject(error)
  }
)