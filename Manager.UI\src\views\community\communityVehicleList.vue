<template>
  <div class="vehicle-list-container">
    <community-vehicle-edit @search="search" ref="editDialog" />
    <div class="card card--search search-flex">
      <el-input v-model="searchModel.plateNumber" placeholder="车牌号" clearable style="width: 200px; margin-right: 16px;" />
      <el-select v-model="searchModel.plateColor" placeholder="车牌颜色" clearable style="width: 150px; margin-right: 16px;">
        <el-option label="蓝色" value="blue" />
        <el-option label="黄色" value="yellow" />
        <el-option label="白色" value="white" />
        <el-option label="黑色" value="black" />
        <el-option label="绿色" value="green" />
      </el-select>
      <el-select v-model="searchModel.status" placeholder="状态" clearable style="width: 150px; margin-right: 16px;">
        <el-option
          v-for="item in vehicleStatusList"
          :key="item.nameEn"
          :label="item.nameCn"
          :value="item.nameEn"
        />
      </el-select>
      <el-button type="primary" @click="search" style="margin-right: 8px;">搜索</el-button>
      <el-button type="primary" @click="add">添加</el-button>
    </div>
    <div class="card card--table">
      <div class="table-col">
        <el-table :data="vehicleList" row-key="id" style="width: 100%; height: 100%;" class="data-table">
          <el-table-column prop="id" label="ID" width="80" align="center"/>
          <el-table-column prop="plateNumber" label="车牌号" align="center"/>
          <el-table-column prop="vehicleColor" label="车辆颜色" width="100" align="center">
          </el-table-column>
          <el-table-column prop="parkingType" label="车位类型" align="center"/>
          <el-table-column prop="parkingNumber" label="车位编号" align="center"/>
          <el-table-column prop="validBeginTime" label="有效期开始" align="center"/>
          <el-table-column prop="validEndTime" label="有效期结束" align="center"/>
          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)" size="small" :color="getStatusColor(scope.row.status)">
                {{ getStatusLabel(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="mainUse" label="主要使用" width="100" align="center">
            <template #default="scope">
              <text v-if="scope.row.mainUse" type="success">是</text>
              <text v-else type="info">否</text>
            </template>
          </el-table-column>
          <el-table-column prop="residentId" label="住户ID" width="100" align="center"/>
       
          <el-table-column prop="createTime" label="创建时间" align="center"/>
          <el-table-column prop="updateTime" label="修改时间" align="center"/>
          <el-table-column label="操作" width="180" align="center">
            <template #default="scope">
              <el-button type="text" size="mini" @click="edit(scope.row.id)">编辑</el-button>
              <el-button type="text" size="mini" @click="deleted(scope.row.id)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination-col">
        <el-pagination background layout="prev, pager, next" @current-change="currentChange" :total="total" :page-size="searchModel.pageSize" :current-page="searchModel.pageNum" />
      </div>
    </div>
  </div>
</template>

<script>
import { listCommunityVehicle, deleteCommunityVehicle, getCommunityVehicle } from '@/api/community/communityVehicle'
import mitt from '@/utils/mitt'
import communityVehicleEdit from '@/components/community/communityVehicleEdit.vue'
import { listDictByNameEn } from "@/api/system/dict";
export default {
  components: { communityVehicleEdit },
  data() {
    return {
      searchModel: {
        pageNum: 1,
        pageSize: 10,
        plateNumber: '',
        vehicleColor: '',
        status: ''

      },
      vehicleStatusList:[],
      vehicleList: [],
      total: 0
    }
  },
  methods: {
    search() {
      listCommunityVehicle(this.searchModel).then(res => {
        this.vehicleList = res.data.data.list
        this.total = res.data.data.total
      })
    },
    add() {
      mitt.emit('openCommunityVehicleEdit')
    },
    edit(id) {
      getCommunityVehicle(id).then(res => {
        mitt.emit('openCommunityVehicleEdit', res.data.data)
      })
    },
    deleted(id) {
      this.$confirm('删除车辆, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteCommunityVehicle(id).then(() => {
          this.search()
          this.$message.success('操作成功')
        })
      }).catch(() => {})
    },
    currentChange(num) {
      this.searchModel.pageNum = num
      this.search()
    },

    
    async init() {
      try {
        const [vehicle_status_res, vehicle_list_res] = await Promise.all([
          listDictByNameEn("vehicle_status"),
          listCommunityVehicle(this.searchModel)
        ]);

        this.vehicleStatusList = vehicle_status_res.data.data;
        this.vehicleList = vehicle_list_res.data.data.list;
        this.total = vehicle_list_res.data.data.total;
      } catch (err) {
        this.$message.error(err.data.errorMessage);
      }
    },

    /**
     * 获取状态标签
     */
    getStatusLabel(status) {
      const option = this.vehicleStatusList.find(item => item.nameEn === status)
      return option ? option.nameCn : status
    },

    /**
     * 获取状态类型
     */
    getStatusType(status) {
      const option = this.vehicleStatusList.find(item => item.nameEn === status)
      if (option && option.cssClass) {
        return '' // 使用自定义颜色时不设置type
      }

      // 默认映射
      const statusMap = {
        'normal': 'success',
        'disabled': 'danger',
        'expired': 'warning'
      }
      return statusMap[status] || 'info'
    },

    /**
     * 获取状态颜色
     */
    getStatusColor(status) {
      const option = this.vehicleStatusList.find(item => item.nameEn === status)
      return option ? option.cssClass : '#909399'
    },

  },
  created() {
    this.init()
  }
}
</script>

<style scoped>
.vehicle-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}
.table-col {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}
.data-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100% !important;
}
.pagination-col {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}
.dark-theme .card {
  background-color: var(--card-background);
  border: 1px solid #444;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
}

.el-table .el-tag {
  border-color: transparent !important;
  color: white;
}
</style> 