import request from '@/utils/request'

// 分页查询用户列表
export function listUser(data) {
    return request({
        url: '/manage-api/v1/user/page',
        method: 'get',
        params: data
    })
}

// 获取当前用户信息
export const currentUser = () =>
    request({
        url: '/manage-api/v1/user/current',
        method: 'get',
        params: {
            _t: Date.now() // 添加时间戳防止缓存
        },
        headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        }
    })

// 添加用户
export function addUser(data) {
    return request({
        url: '/manage-api/v1/user',
        method: 'post',
        data
    })
}

// 编辑用户
export function editUser(data) {
    return request({
        url: '/manage-api/v1/user',
        method: 'put',
        data
    })
}

// 删除用户
export function deleteUser(id) {
    return request({
        url: '/manage-api/v1/user',
        method: 'delete',
        params: { id }
    })
}

// 获取用户详情
export function getUser(id) {
    return request({
        url: '/manage-api/v1/user',
        method: 'get',
        params: { id }
    })
}

// 修改用户密码
export function updateUserPassword(data) {
    return request({
        url: '/manage-api/v1/user/password',
        method: 'put',
        data
    })
}

// 修改当前用户密码
export function updateCurrentUserPassword(data) {
    return request({
        url: '/manage-api/v1/user/password',
        method: 'post',
        data
    })
}