# 路由守卫 Token 处理优化

## 🔍 问题分析

用户反馈：路由守卫中对 token 的操作与 `request.js` 拦截器重复，导致 token 过期后直接跳转登录页，没有给 request.js 拦截器处理的机会。

### 原有问题：
1. **重复的 Token 检查**：路由守卫和 request.js 都在检查 token 过期
2. **冲突的跳转逻辑**：路由守卫可能在 request.js 拦截器之前就跳转了
3. **职责不清**：路由守卫应该专注于路由和权限，而不是 token 管理

## 📋 Request.js 拦截器功能

### 已有的完善 Token 处理：
```javascript
// 请求拦截器
service.interceptors.request.use((config) => {
    let token = getToken()
    
    if (token) {
        // 判断token是否过期,如果过期请求刷新token
        if (isTokenExpired(token)) {
            if (!isRefreshing) {
                isRefreshing = true
                // 刷新token
                return refreshToken().then((newToken) => {
                    isRefreshing = false
                    config.headers['Authorization'] = newToken
                    return config
                })
            } else {
                // 如果正在刷新token，将请求加入队列
                return new Promise((resolve) => {
                    subscribeTokenRefresh((newToken) => {
                        config.headers['Authorization'] = newToken
                        resolve(config)
                    })
                })
            }
        } else {
            // token未过期，直接添加到请求头
            const authData = JSON.parse(token)
            config.headers['Authorization'] = authData.access_token
            return config
        }
    }
})

// 响应拦截器
service.interceptors.response.use(
    async response => {
        let code = response.data.code
        if (code == 401 || code == 402) {
            console.log('接口返回401/402，token过期！')
            reLogin() // 清除数据并跳转登录页
            return Promise.reject(response)
        }
    },
    error => {
        if (error.response && (error.response.status === 401 || error.response.status === 402)) {
            console.log('请求返回401/402，token过期！')
            reLogin() // 清除数据并跳转登录页
            return Promise.reject(error)
        }
    }
)
```

### Request.js 的完整功能：
- ✅ **Token 过期检测**：自动检查 token 是否过期
- ✅ **Token 自动刷新**：使用 refresh_token 自动刷新
- ✅ **请求队列管理**：刷新期间的请求排队处理
- ✅ **401/402 处理**：自动处理服务器返回的认证失败
- ✅ **自动跳转登录**：认证失败时清除数据并跳转

## ✅ 路由守卫优化

### 优化前的问题：
```javascript
// 路由守卫中重复的 token 处理
let token = window.$local?.get('smartPropertyToken')

// 检查 token 格式和有效性
if (token && typeof token === 'string') {
    try {
        const tokenObj = JSON.parse(token)
        if (tokenObj.expires_time && tokenObj.expires_time < Date.now()) {
            console.log('路由守卫: Token 已过期')
            token = null
        }
    } catch (e) {
        console.log('路由守卫: Token 解析失败')
        token = null
    }
}

// 如果没有 token，重定向到登录页
if (!hasToken) {
    console.log('没有有效 token，重定向到登录页')
    window.$local?.removeAll()  // 与 request.js 冲突
    data.logined = false
    return next("/login")
}
```

### 优化后的简化逻辑：
```javascript
// 路由守卫专注于路由和权限管理
router.beforeEach(async (to, from, next) => {
    // 如果访问登录页，直接允许
    if (to.path === "/login") {
        return next()
    }

    // 简单检查是否有 token（不检查过期，由 request.js 处理）
    const token = window.$local?.get('smartPropertyToken')
    const hasBasicToken = token && token !== 'null' && token !== 'undefined'

    // 如果完全没有 token，重定向到登录页
    if (!hasBasicToken) {
        console.log('没有 token，重定向到登录页')
        return next("/login")
    }

    // 如果有 token 但还没有加载用户信息，先加载
    if (hasBasicToken && !data.logined) {
        try {
            await onLoad(to.path)
            return next(to.fullPath)
        } catch (error) {
            // 不在这里清除数据，让 request.js 的拦截器处理
            console.error('加载用户信息时出错:', error)
            return next("/login")
        }
    }

    // 检查目标路由是否存在，必要时恢复动态路由
    // ... 路由恢复逻辑
})
```

## 🎯 优化要点

### 1. 职责分离
- **Request.js**：负责 token 管理、过期检测、自动刷新、认证失败处理
- **路由守卫**：负责路由权限、动态路由恢复、页面访问控制

### 2. 移除重复逻辑
- ❌ 移除路由守卫中的 token 过期检测
- ❌ 移除路由守卫中的 token 解析和验证
- ❌ 移除路由守卫中的数据清除操作

### 3. 简化 Token 检查
- ✅ 只检查 token 是否存在（基础检查）
- ✅ 不检查 token 格式和过期时间
- ✅ 让 request.js 处理所有 token 相关逻辑

### 4. 错误处理优化
- ✅ 用户信息加载失败时不清除数据
- ✅ 让 request.js 统一处理认证错误
- ✅ 避免路由守卫和拦截器的冲突

## 🔄 新的执行流程

### Token 过期场景：
1. **用户访问页面** → 路由守卫检查基础 token 存在 → 允许继续
2. **页面发起 API 请求** → request.js 检测 token 过期 → 自动刷新 token
3. **刷新成功** → 继续原请求 → 页面正常显示
4. **刷新失败** → request.js 跳转登录页

### 完全无 Token 场景：
1. **用户访问页面** → 路由守卫检查无 token → 直接跳转登录页

### 用户信息加载失败场景：
1. **路由守卫加载用户信息失败** → 跳转登录页
2. **不清除 token 数据** → 让 request.js 判断是否需要清除

## 📊 优化效果

### 解决的问题：
- ✅ **避免重复处理**：路由守卫不再重复检查 token 过期
- ✅ **避免冲突**：路由守卫不会抢在 request.js 之前跳转
- ✅ **职责清晰**：各组件专注自己的职责
- ✅ **更好的用户体验**：token 过期时能自动刷新而不是直接跳转

### 保留的功能：
- ✅ **基础权限控制**：无 token 时仍然跳转登录页
- ✅ **动态路由恢复**：页面刷新后正确恢复路由
- ✅ **用户信息加载**：首次访问时加载用户信息和权限

## 🧪 测试验证

### 测试场景：
1. **Token 过期测试**：
   - 手动设置 token 过期时间
   - 访问页面，观察是否自动刷新而不是跳转登录

2. **无 Token 测试**：
   - 清除所有 token
   - 访问受保护页面，应该跳转登录页

3. **页面刷新测试**：
   - 在动态路由页面刷新
   - 应该正确恢复路由和显示页面

### 预期结果：
- ✅ Token 过期时自动刷新，用户无感知
- ✅ 只有在 token 完全无效时才跳转登录页
- ✅ 页面刷新后正常工作
- ✅ 不再出现路由守卫和拦截器的冲突

路由守卫 Token 处理优化完成，现在系统的认证流程更加合理和稳定！
