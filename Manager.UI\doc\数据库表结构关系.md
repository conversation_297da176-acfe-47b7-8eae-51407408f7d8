# 数据库表结构与关系

本文档梳理了项目中主要表及其外键关系，按功能模块分类。

---

## 1. 系统管理模块

- `sys_user`
  - 外键：
    - `role_id` → `sys_role.id`
    - `org_id` → `sys_org.id`

- `sys_role`
  - 外键：
    - `parent_id` → `sys_role.id`（角色树）

- `sys_org`
  - 外键：
    - `parent_id` → `sys_org.id`（组织结构树）

- `sys_menu`
  - 外键：
    - `parent_id` → `sys_menu.id`（菜单树）

- `sys_permission`
  - 主要用于接口/按钮权限映射，无直接外键

- `sys_role_menu`
  - 关联表：
    - `role_id` → `sys_role.id`
    - `menu_id` → `sys_menu.id`

- `sys_imagetext`
  - 字幕/轮播图内容表
  - 外键：
    - `create_by` → `sys_user.id`

- `sys_notice`
  - 通知模板表
  - 外键：
    - `create_by` → `sys_user.id`

- `sys_notice_read`
  - 已读记录表
  - 外键：
    - `user_id` → `sys_user.id`
    - `message_id` → `sys_notice.id`

- `sys_log`
  - 审计日志
  - 外键：
    - `user_id` → `sys_user.id`

- `t_message`
  - 即时会话消息
  - 多态外键（系统用户或小程序用户）:
    - `from_user_id` → `sys_user.id` / `t_member.id`
    - `to_user_id` → `sys_user.id` / `t_member.id`

---

## 2. 企业/租户管理模块

- `company`
  - 租户（公司）主表

- `company_dept`
  - 外键：
    - `parent_id` → `company_dept.id`
    - `company_id` → `company.id`

- `company_role`
  - 外键：
    - `parent_id` → `company_role.id`
    - `company_id` → `company.id`

- `company_position`
  - 外键：
    - `company_id` → `company.id`

- `company_user`
  - 外键：
    - `dept_id` → `company_dept.id`
    - `company_id` → `company.id`

- `company_person`
  - 员工/人员维表
  - 外键：
    - `dept_id` → `company_dept.id`
    - `company_id` → `company.id`
    - `position_code` → `company_position.id`

- `company_role_menu`
  - 租户角色与菜单关联
  - 外键：
    - `role_id` → `company_role.id`
    - `menu_id` → `sys_menu.id`

---

## 3. 社区/物业管理模块

- `t_community`
  - 社区（小区）主表

- `t_community_building`
  - 外键：
    - `community_id` → `t_community.id`

- `t_community_room`
  - 外键：
    - `building_id` → `t_community_building.id`
    - `community_id` → `t_community.id`（冗余）

- `t_community_resident`
  - 入住人员表
  - 外键：
    - `community_id` → `t_community.id`

- `t_community_resident_room`
  - 关联表：
    - `resident_id` → `t_community_resident.id`
    - `room_id` → `t_community_room.id`

- `t_resident_family`
  - 家庭成员表
  - 外键：
    - `resident_id` → `t_community_resident.id`
    - `family_resident_id` → `t_community_resident.id`
    - `room_id` → `t_community_room.id`

- `t_member`
  - 小程序/公众号用户
  - 外键：
    - `community_id` → `t_community.id`

- `t_member_resident`
  - 用户与居民关联
  - 外键：
    - `user_id` → `t_member.id`
    - `resident_id` → `t_community_resident.id`

- `t_community_vehicle`
  - 车牌信息
  - 外键：
    - `resident_id` → `t_community_resident.id`

- `t_community_visitor`
  - 访客表
  - 外键：
    - `resident_id` → `t_community_resident.id`

- `t_community_work_order`
  - 工单表
  - 外键：
    - `resident_id` → `t_community_resident.id`
    - `person_id` → `company_person.id`

---

## 4. 物业账单与缴费模块

- `t_property_payment_items`
  - 收费项目表
  - 外键：
    - `community_id` → `t_community.id`

- `t_property_bill`
  - 账单主表
  - 外键：
    - `resident_id` → `t_community_resident.id`
    - `building_id` → `t_community_building.id`
    - `open_id` → `t_member.id`（可选）

- `t_property_bill_detail`
  - 账单明细
  - 外键：
    - `bill_id` → `t_property_bill.id`
    - `payment_item` → `t_property_payment_items.id`

---

## 5. 电商/物品申领模块

- `t_good_stuff`
  - 物品列表

- `t_good_stuff_order_user`
  - 下单记录
  - 外键：
    - `user_id` → `t_member.id`

- `t_good_stuff_collect`
  - 收藏表
  - 外键：
    - `user_id` → `t_member.id`
    - `good_stuff_id` → `t_good_stuff.id`

---

## 6. 消息通知与日志模块

- `sys_notice`
  - 通知模板表（前述）

- `sys_notice_read`
  - 已读记录表（前述）

- `sys_log`
  - 审计日志（前述）

- `sys_imagetext`
  - 图文/轮播图（前述）

- `t_message`
  - 会话消息（前述） 