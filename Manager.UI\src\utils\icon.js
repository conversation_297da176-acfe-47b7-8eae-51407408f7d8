// Element Plus 图标系统 - Vue3 专用
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// Element Plus 图标列表
export const elementPlusIcons = [
  // 基础图标
  { name: '首页', component: 'House' },
  { name: '用户', component: 'User' },
  { name: '设置', component: 'Setting' },
  { name: '文档', component: 'Document' },
  { name: '数据线', component: 'DataLine' },
  { name: '监控', component: 'Monitor' },
  { name: '工具', component: 'Tools' },
  { name: '通知', component: 'Bell' },
  { name: '消息', component: 'Message' },
  { name: '日历', component: 'Calendar' },

  // 文件相关
  { name: '文件夹', component: 'Folder' },
  { name: '文件', component: 'Files' },
  { name: '打开文件夹', component: 'FolderOpened' },
  { name: '添加文件夹', component: 'FolderAdd' },
  { name: '删除文件夹', component: 'FolderRemove' },
  { name: '文档添加', component: 'DocumentAdd' },
  { name: '文档删除', component: 'DocumentRemove' },
  { name: '文档复制', component: 'DocumentCopy' },

  // 操作图标
  { name: '添加', component: 'Plus' },
  { name: '编辑', component: 'Edit' },
  { name: '删除', component: 'Delete' },
  { name: '查看', component: 'View' },
  { name: '搜索', component: 'Search' },
  { name: '刷新', component: 'Refresh' },
  { name: '下载', component: 'Download' },
  { name: '上传', component: 'Upload' },
  { name: '关闭', component: 'Close' },
  { name: '确认', component: 'Check' },

  // 方向图标
  { name: '向左', component: 'ArrowLeft' },
  { name: '向右', component: 'ArrowRight' },
  { name: '向上', component: 'ArrowUp' },
  { name: '向下', component: 'ArrowDown' },
  { name: '左三角', component: 'CaretLeft' },
  { name: '右三角', component: 'CaretRight' },
  { name: '上三角', component: 'CaretTop' },
  { name: '下三角', component: 'CaretBottom' },

  // 系统功能
  { name: '菜单', component: 'Menu' },
  { name: '网格', component: 'Grid' },
  { name: '列表', component: 'List' },
  { name: '排序', component: 'Sort' },
  { name: '排名', component: 'Rank' },
  { name: '分享', component: 'Share' },
  { name: '加载中', component: 'Loading' },
  { name: '管理', component: 'Management' },
  { name: '操作', component: 'Operation' },
  { name: '权限', component: 'Key' },

  // 媒体图标
  { name: '图片', component: 'Picture' },
  { name: '相机', component: 'Camera' },
  { name: '摄像机', component: 'VideoCamera' },
  { name: '麦克风', component: 'Microphone' },

  // 商务图标
  { name: '商品', component: 'Goods' },
  { name: '购物车', component: 'ShoppingCart' },
  { name: '礼物', component: 'Present' },
  { name: '硬币', component: 'Coin' },
  { name: '钱包', component: 'Wallet' },
  { name: '价格标签', component: 'PriceTag' },

  // 通讯图标
  { name: '电话', component: 'Phone' },
  { name: '聊天圆点', component: 'ChatDotRound' },
  { name: '聊天线条', component: 'ChatLineRound' },
  { name: '明信片', component: 'Postcard' },
  { name: '位置', component: 'Position' },

  // 时间图标
  { name: '计时器', component: 'Timer' },
  { name: '闹钟', component: 'AlarmClock' },
  { name: '时钟', component: 'Clock' },
  { name: '秒表', component: 'Stopwatch' },

  // 位置图标
  { name: '定位', component: 'Location' },
  { name: '位置信息', component: 'LocationInformation' },
  { name: '添加位置', component: 'AddLocation' },
  { name: '删除位置', component: 'DeleteLocation' },
  { name: '地图位置', component: 'MapLocation' },

  // 安全图标
  { name: '锁定', component: 'Lock' },
  { name: '解锁', component: 'Unlock' },
  { name: '隐藏', component: 'Hide' },

  // 工具图标
  { name: '魔法棒', component: 'MagicStick' },
  { name: '画笔', component: 'Brush' },
  { name: '剪刀', component: 'Scissors' },
  { name: '复制文档', component: 'CopyDocument' },
  { name: '全屏', component: 'FullScreen' },

  // 娱乐图标
  { name: '篮球', component: 'Basketball' },
  { name: '足球', component: 'Football' },
  { name: '奖杯', component: 'Trophy' },
  { name: '奖牌', component: 'Medal' },

  // 天气图标
  { name: '晴天', component: 'Sunny' },
  { name: '多云', component: 'Cloudy' },
  { name: '闪电', component: 'Lightning' },
  { name: '雨伞', component: 'Umbrella' },

  // 食物图标
  { name: '咖啡', component: 'Coffee' },
  { name: '冰淇淋', component: 'IceCream' },
  { name: '苹果', component: 'Apple' },
  { name: '橙子', component: 'Orange' }
]

// 获取图标组件
export function getIconComponent(iconName) {
  return ElementPlusIconsVue[iconName] || null
}

// 导出所有图标组件
export { ElementPlusIconsVue }
