<template>
  <el-dialog
    :title="`${residentInfo.residentName} - 房产管理`"
    v-model="dialog.show"
    width="900px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="property-management">
      <!-- 住户基本信息 -->
      <div class="resident-info">
        <el-descriptions :column="3" border>
          <el-descriptions-item label="住户姓名">{{
            residentInfo.residentName
          }}</el-descriptions-item>
          <el-descriptions-item label="手机号码">{{
            residentInfo.phone
          }}</el-descriptions-item>
          <el-descriptions-item label="证件号码">{{
            residentInfo.idCardNumber
          }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 操作按钮 -->
      <div class="toolbar">
        <el-button type="primary" @click="openPropertyEdit()" icon="Plus">
          新增房产
        </el-button>
        <el-button @click="refreshList" icon="Refresh">刷新</el-button>
      </div>

      <!-- 房产列表 -->
      <el-table
        :data="propertyList"
        v-loading="loading"
        border
        stripe
        style="width: 100%"
      >
        <el-table-column prop="buildingNumber" label="楼栋" width="120"  align="center"/>
        <el-table-column prop="unitNumber" label="单元" width="120"  align="center"/>
        <el-table-column prop="roomNumber" label="房号" width="120"  align="center"/>
        <el-table-column prop="isDefault" label="默认房产" width="117"  align="center">
          <template #default="{ row }">
            {{ row.isDefault ? "是" : "否" }}
          </template>
        </el-table-column>
        <el-table-column prop="residentType" label="住户类型" width="120" align="center">
          <template #default="scope">
            <el-tag :type="getResidentTypeTag(scope.row.residentType)">
              {{ getResidentTypeLabel(scope.row.residentType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="120" align="center">
          <template #default="scope">
            <el-tag
              :type="getStatusTagType(scope.row.status)"
              :style="getStatusCustomStyle(scope.row.status)"
            >
              {{ getStatusLabel(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right"  align="center">
          <template #default="scope">
            <el-button
              class="edit-btn"
           
              size="small"
              @click="openPropertyEdit(scope.row)"
              icon="Edit"
            >
              编辑
            </el-button>
            <el-button
              class="edit-btn"
              
              size="small"
              @click="handleDelete(scope.row)"
              icon="Delete"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="searchModel.pageNum"
          v-model:page-size="searchModel.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 房产编辑对话框 -->
    <CommunityResidentPropertyEdit
      ref="propertyEditRef"
      @refresh="loadPropertyList"
    />

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialog.show = false">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import {
  listResidentProperty,
  deleteResidentProperty,
} from "@/api/community/communityResidentProperty";
import { RESIDENT_TYPES } from "@/api/community/communityResident";
import { getSelectedCommunityId } from "@/store/modules/options";
import { eventManagerMixin } from "@/utils/eventManager";
import { listDictByNameEn } from "@/api/system/dict";
import CommunityResidentPropertyEdit from "./communityResidentPropertyEdit.vue";

export default {
  name: "CommunityResidentProperty",
  mixins: [eventManagerMixin],
  components: {
    CommunityResidentPropertyEdit,
  },
  data() {
    return {
      dialog: {
        show: false,
      },
      residentInfo: {
        id: null,
        residentName: "",
        phone: "",
        idCardNumber: "",
      },
      propertyList: [],
      loading: false,
      total: 0,
      searchModel: {
        pageNum: 1,
        pageSize: 10,
        residentId: null,
        communityId: null,
      },
      // 字典数据
      residentStatusList: [],
    };
  },

  methods: {
    /**
     * 初始化字典数据
     */
    async initDictData() {
      try {
        const res = await listDictByNameEn("resident_status");
        this.residentStatusList = res.data.data || [];
      } catch (err) {
        console.error("加载住户状态字典失败:", err);
        // 如果字典加载失败，使用默认状态
        this.residentStatusList = [
          { nameEn: "normal", nameCn: "正常", cssClass: "#67C23A" },
          { nameEn: "pending", nameCn: "待审核", cssClass: "#E6A23C" },
          { nameEn: "move_out", nameCn: "搬出", cssClass: "#909399" },
        ];
      }
    },

    /**
     * 加载房产列表
     */
    loadPropertyList() {
      if (!this.residentInfo.id) return;
      ;
      this.loading = true;
      const params = {
        ...this.searchModel,
        residentId: this.residentInfo.id,
        communityId: getSelectedCommunityId(),
      };

      listResidentProperty(params)
        .then((res) => {
          this.propertyList = res.data.data.list || [];
          this.total = res.data.data.total || 0;
        })
        .catch((err) => {
          console.error("加载房产列表失败:", err);
          this.$message.error(err.data?.errorMessage || "加载失败");
          this.propertyList = [];
          this.total = 0;
        })
        .finally(() => {
          this.loading = false;
        });
    },

    /**
     * 刷新列表
     */
    refreshList() {
      this.searchModel.pageNum = 1;
      this.loadPropertyList();
    },

    /**
     * 打开房产编辑对话框
     */
    openPropertyEdit(property = null) {
      this.$refs.propertyEditRef.open(this.residentInfo, property);
    },

    /**
     * 删除房产
     */
    handleDelete(property) {
      ;
      this.$confirm(`确定要删除房产"${property.roomNumber}"吗？`, "删除确认", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        var params = {
          residentId: this.residentInfo.residentId,
          id: property.id,
        };
        deleteResidentProperty(params)
          .then(() => {
            this.$message.success("删除成功");
            this.loadPropertyList();
          })
          .catch((err) => {
            this.$message.error(err.data?.errorMessage || "删除失败");
          });
      });
    },

    /**
     * 分页大小变化
     */
    handleSizeChange(size) {
      this.searchModel.pageSize = size;
      this.searchModel.pageNum = 1;
      this.loadPropertyList();
    },

    /**
     * 当前页变化
     */
    handleCurrentChange(page) {
      this.searchModel.pageNum = page;
      this.loadPropertyList();
    },

    /**
     * 获取住户类型标签样式
     */
    getResidentTypeTag(type) {
      const tagMap = {
        owner: "success",
        tenant: "warning",
        family: "info",
      };
      return tagMap[type] || "";
    },

    /**
     * 获取住户类型标签文本
     */
    getResidentTypeLabel(type) {
      const item = RESIDENT_TYPES.find((item) => item.value === type);
      return item ? item.label : type;
    },

    /**
     * 获取状态标签类型
     */
    getStatusTagType(status) {
      const dictItem = this.residentStatusList.find(
        (item) => item.nameEn === status
      );
      if (dictItem?.cssClass) {
        // 如果字典中有自定义颜色，返回空字符串让自定义样式生效
        if (dictItem.cssClass.startsWith("#")) {
          return "";
        }
        // 如果是Element Plus支持的类型
        const elementTypes = [
          "primary",
          "success",
          "warning",
          "danger",
          "info",
        ];
        if (elementTypes.includes(dictItem.cssClass)) {
          return dictItem.cssClass;
        }
      }

      // 默认映射
      const tagMap = {
        normal: "success",
        pending: "warning",
        move_out: "info",
      };
      return tagMap[status] || "";
    },

    /**
     * 获取状态标签文本
     */
    getStatusLabel(status) {
      const dictItem = this.residentStatusList.find(
        (item) => item.nameEn === status
      );
      return dictItem ? dictItem.nameCn : status;
    },

    /**
     * 获取状态标签自定义样式
     */
    getStatusCustomStyle(status) {
      const dictItem = this.residentStatusList.find(
        (item) => item.nameEn === status
      );

      if (dictItem?.cssClass && dictItem.cssClass.startsWith("#")) {
        return {
          backgroundColor: dictItem.cssClass,
          borderColor: dictItem.cssClass,
          color: "#fff",
        };
      }

      return {};
    },
  },

  mounted() {
    // 初始化字典数据
    this.initDictData();

    // 使用安全的事件监听器注册
    this.$safeOn("openResidentProperty", (residentInfo) => {
      console.log("打开房产管理对话框");
      this.residentInfo = { ...residentInfo };
      this.dialog.show = true;
      this.refreshList();
    });
  },
};
</script>

<style scoped>
.property-management {
  padding: 0;
}

.resident-info {
  margin-bottom: 20px;
}

.toolbar {
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.text-muted {
  color: #999;
}

.dialog-footer {
  text-align: right;
}

.edit-btn {
  width: 40px;
}
</style>
