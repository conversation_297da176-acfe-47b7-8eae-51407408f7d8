# 好物状态字典集成

## 📋 更新概述

根据用户需求，将好物列表的硬编码状态改为使用字典管理，统一使用 `status` 字段，支持十六进制颜色显示和必填原因验证。

## ✅ 实现内容

### 🔧 1. 统一状态管理

#### 好物状态字典
- **字典名称**: `good_stuff_status`
- **用途**: 管理好物的所有状态（审核、上架等）
- **字段**: `status`
- **颜色**: 使用 `cssClass` 十六进制颜色值

#### 状态类型
- **上架**: `list` - #9C27B0 (紫色)
- **下架**: `un_list` - #E6A23C (橙色)
- **待审核**: `pending` - #409EFF (蓝色)
- **通过**: `pass` - #67C23A (绿色)
- **不通过**: `no_pass` - #F56C6C (红色)

### 🔧 2. 操作分离设计

#### 审核操作
- **待审核** (`pending`): 设置为待审核状态
- **通过** (`pass`): 审核通过，可进行上架操作
- **不通过** (`no_pass`): 审核不通过，需填写原因

#### 上架下架操作
- **上架** (`list`): 仅在审核通过状态下可操作
- **下架** (`un_list`): 仅在上架状态下可操作，需填写原因

### 🔧 3. API接口统一

#### 审核和上架下架接口
```javascript
PUT /manage-api/v1/good-stuff/examine
{
  id: 好物id,
  status: 好物状态字典nameEn,
  examineNote: 状态变更原因/备注
}
```

#### 必填原因规则
- **下架** (`un_list`): 必须填写下架原因
- **不通过** (`no_pass`): 必须填写审核不通过原因
- **其他状态**: 备注可选

### 🔧 4. 前端代码修改

#### 字典数据初始化
```javascript
async initDictData() {
  try {
    // 加载好物状态字典
    const statusRes = await listDictByNameEn('good_stuff_status')
    this.statusOptions = (statusRes.data.data || []).map(item => ({
      label: item.nameCn,
      value: item.nameEn,
      cssClass: item.cssClass // 保存颜色信息
    }))
  } catch (err) {
    console.error('加载数据字典失败:', err)
  }
}
```

#### 状态显示方法
```javascript
// 状态标签
getStatusLabel(status) {
  const option = this.statusOptions.find(item => item.value === status)
  return option ? option.label : status
}

// 状态颜色（十六进制）
getStatusColor(status) {
  const option = this.statusOptions.find(item => item.value === status)
  return option && option.cssClass ? option.cssClass : null
}

// 状态类型（Element Plus tag组件）
getStatusType(status) {
  const option = this.statusOptions.find(item => item.value === status)
  if (option && option.cssClass) {
    return '' // 使用自定义颜色时不设置type
  }
  // 默认映射
  const statusMap = {
    'list': 'success',
    'un_list': 'warning',
    'pending': 'info',
    'pass': 'success',
    'no_pass': 'danger'
  }
  return statusMap[status] || 'info'
}
```

#### 操作方法分离
```javascript
// 审核操作
examineGoods(row, newStatus) {
  const needReason = newStatus === 'no_pass'
  const validator = needReason ? (value) => {
    if (!value || value.trim() === '') {
      return '请输入审核不通过原因'
    }
    return true
  } : null

  this.$prompt(`确定要将这个好物${actionText}吗？`, '审核确认', {
    inputValidator: validator
  }).then(({ value: examineNote }) => {
    const examineData = {
      id: row.id,
      status: newStatus,
      examineNote: examineNote || ''
    }

    examineGoodStuff(examineData).then(res => {
      this.search()
      this.$message.success(`商品审核状态已${actionText}`)
    })
  })
}

// 上架操作
listGoods(row) {
  this.$confirm('确定要上架这个好物吗？', '上架确认').then(() => {
    const listData = {
      id: row.id,
      status: 'list',
      examineNote: '商品上架'
    }

    examineGoodStuff(listData).then(res => {
      this.search()
      this.$message.success('商品已成功上架')
    })
  })
}

// 下架操作
unlistGoods(row) {
  this.$prompt('请输入下架原因：', '下架确认（必须填写原因）', {
    inputValidator: (value) => {
      if (!value || value.trim() === '') {
        return '请输入下架原因'
      }
      return true
    }
  }).then(({ value: examineNote }) => {
    const unlistData = {
      id: row.id,
      status: 'un_list',
      examineNote: examineNote
    }

    examineGoodStuff(unlistData).then(res => {
      this.search()
      this.$message.success('商品已成功下架')
    })
  })
}
```

### 🔧 5. 界面更新

#### 表格列更新
```vue
<!-- 状态列 -->
<el-table-column prop="status" label="状态" width="100" align="center">
  <template #default="scope">
    <el-tag
      :type="getStatusType(scope.row.status)"
      :color="getStatusColor(scope.row.status)"
      size="small"
    >
      {{ getStatusLabel(scope.row.status) }}
    </el-tag>
  </template>
</el-table-column>
```

#### 搜索条件更新
```vue
<el-select
  v-model="searchModel.status"
  placeholder="状态"
  clearable
>
  <el-option label="全部" value="" />
  <el-option
    v-for="item in statusOptions"
    :key="item.value"
    :label="item.label"
    :value="item.value"
  />
</el-select>
```

#### 操作按钮分离设计
```vue
<!-- 审核操作 -->
<el-dropdown @command="(command) => examineGoods(scope.row, command)">
  <el-button type="text" size="small">
    审核<el-icon class="el-icon--right"><arrow-down /></el-icon>
  </el-button>
  <template #dropdown>
    <el-dropdown-menu>
      <el-dropdown-item v-if="scope.row.status !== 'pending'" command="pending">
        <el-icon><clock /></el-icon>待审核
      </el-dropdown-item>
      <el-dropdown-item v-if="scope.row.status !== 'pass'" command="pass">
        <el-icon><check /></el-icon>通过
      </el-dropdown-item>
      <el-dropdown-item v-if="scope.row.status !== 'no_pass'" command="no_pass">
        <el-icon><close /></el-icon>不通过
      </el-dropdown-item>
    </el-dropdown-menu>
  </template>
</el-dropdown>

<!-- 上架下架操作 -->
<el-button
  v-if="scope.row.status === 'pass'"
  type="text"
  size="small"
  @click="listGoods(scope.row)"
>
  上架
</el-button>
<el-button
  v-if="scope.row.status === 'list'"
  type="text"
  size="small"
  @click="unlistGoods(scope.row)"
>
  下架
</el-button>
```

## 📊 功能特点

### ✅ 字典驱动
- **动态配置**: 状态选项从字典表获取，便于管理
- **实时更新**: 字典数据变更时前端自动同步
- **多语言支持**: nameEn存储，nameCn显示

### ✅ 操作分离
- **审核操作**: 待审核、通过、不通过三个状态
- **上架操作**: 仅在审核通过后可进行上架
- **下架操作**: 仅在上架状态下可进行下架
- **权限控制**: 不同状态下显示不同操作按钮

### ✅ 流程控制
- **审核流程**: 待审核 → 通过/不通过
- **上架流程**: 审核通过 → 上架 → 下架
- **权限验证**: 状态间的转换有严格的前置条件
- **原因记录**: 关键操作需要填写原因

### ✅ 用户体验
- **直观显示**: 状态标签颜色区分
- **便捷操作**: 下拉菜单快速切换状态
- **确认机制**: 状态变更前确认操作

## 🎯 使用场景

### 审核管理
1. 管理员查看待审核好物
2. 选择审核操作（待审核/通过/不通过）
3. 审核不通过时必须填写原因
4. 提交审核结果

### 上架管理
1. 审核通过的好物可进行上架
2. 上架状态的好物可进行下架
3. 下架时必须填写下架原因
4. 状态变更实时生效

### 权限控制
1. 不同状态下显示不同操作按钮
2. 严格的状态转换流程控制
3. 关键操作需要原因验证
4. 防止非法状态转换

## 🔧 技术实现

### 数据结构
```javascript
// 字典数据结构
{
  nameEn: 'pending',     // 存储值
  nameCn: '待审核',       // 显示值
  cssClass: '#409EFF'    // 颜色值
}

// 操作请求数据
{
  id: 123,
  status: 'pass',        // 状态值
  examineNote: '审核通过，商品信息完整'  // 操作原因/备注
}
```

### 状态流转
```
待审核(pending) → 通过(pass) → 上架(list)
                ↓              ↓
            不通过(no_pass)   下架(un_list)
```

### 颜色映射
```javascript
const colorTypeMap = {
  '#E6A23C': 'warning',  // 橙色 -> 警告
  '#67C23A': 'success',  // 绿色 -> 成功
  '#F56C6C': 'danger',   // 红色 -> 危险
  '#409EFF': 'primary'   // 蓝色 -> 主要
}
```

## ✅ 完成状态

- ✅ **字典API集成** - 完成
- ✅ **操作分离设计** - 完成
- ✅ **审核接口统一** - 完成
- ✅ **权限控制逻辑** - 完成
- ✅ **必填原因验证** - 完成
- ✅ **界面交互优化** - 完成
- ✅ **状态流转控制** - 完成

好物管理已成功实现审核和上架下架操作分离，支持严格的状态流转控制和权限验证！🎉
