<template>
  <div class="visitor-list-container">
    <!-- 搜索区域 -->
    <div class="card card--search search-flex">
      <el-input
        v-model="searchModel.visitorName"
        placeholder="访客姓名"
        clearable
        style="width: 200px; margin-right: 16px;"
      />
      <el-input
        v-model="searchModel.phone"
        placeholder="手机号"
        clearable
        style="width: 200px; margin-right: 16px;"
      />
      <el-input
        v-model="searchModel.residentName"
        placeholder="被访住户"
        clearable
        style="width: 200px; margin-right: 16px;"
      />
      <el-select
        v-model="searchModel.status"
        placeholder="访客状态"
        clearable
        style="width: 120px; margin-right: 16px;"
      >
        <el-option label="全部" value="" />
        <el-option label="待访问" value="pending" />
        <el-option label="已访问" value="visited" />
        <el-option label="已过期" value="expired" />
      </el-select>
      <el-button type="primary" @click="search" style="margin-right: 8px;">搜索</el-button>
      <el-button @click="resetSearch">重置</el-button>
      <el-button type="primary" @click="add">添加访客</el-button>
    </div>

    <!-- 表格区域 -->
    <div class="card card--table">
      <div class="table-col">
        <el-table
          :data="visitorList"
          row-key="id"
          style="width: 100%; height: 100%;"
          class="data-table"
          v-loading="loading"
        >
          <el-table-column prop="id" label="ID" width="80" align="center"/>
          <el-table-column prop="visitorName" label="访客姓名" width="120" align="center"/>
          <el-table-column prop="phone" label="手机号" width="140" align="center"/>
          <el-table-column prop="residentName" label="被访住户" width="120" align="center"/>
          <el-table-column prop="residentAddress" label="访问地址" align="center" min-width="150" show-overflow-tooltip/>
          <el-table-column prop="purpose" label="访问目的" width="120" align="center"/>
          <el-table-column prop="visitTime" label="来访时间" width="160" align="center"/>
          <el-table-column prop="stayDuration" label="停留时长" width="100" align="center">
            <template #default="scope">
              {{ scope.row.stayDuration }}{{ scope.row.timeUnit === 'hours' ? '小时' : scope.row.timeUnit }}
            </template>
          </el-table-column>
          <el-table-column prop="vehicleNumber" label="车牌号" width="120" align="center"/>
          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)" size="small">
                {{ getStatusLabel(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="isUsual" label="常用" width="80" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.isUsual ? 'success' : 'info'" size="small">
                {{ scope.row.isUsual ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" align="center">
            <template #default="scope">
              <el-button type="text" size="small" @click="viewDetail(scope.row)">查看</el-button>
              <el-button type="text" size="small" @click="edit(scope.row)">编辑</el-button>
              <el-button type="text" size="small" @click="deleted(scope.row.id)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @current-change="currentChange"
          :current-page="searchModel.pageNum"
          :page-size="searchModel.pageSize"
          layout="total, prev, pager, next, jumper"
          :total="total"
        />
      </div>
    </div>

    <!-- 详情弹窗 -->
    <el-dialog
      v-model="detailDialog.visible"
      :title="detailDialog.title"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="detailDialog.data" class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="访客ID">{{ detailDialog.data.id }}</el-descriptions-item>
          <el-descriptions-item label="访客姓名">{{ detailDialog.data.visitorName }}</el-descriptions-item>
          <el-descriptions-item label="手机号">{{ detailDialog.data.phone }}</el-descriptions-item>
          <el-descriptions-item label="车牌号">{{ detailDialog.data.vehicleNumber || '无' }}</el-descriptions-item>
          <el-descriptions-item label="被访住户">{{ detailDialog.data.residentName }}</el-descriptions-item>
          <el-descriptions-item label="住户手机">{{ detailDialog.data.residentPhone }}</el-descriptions-item>
          <el-descriptions-item label="访问地址" :span="2">{{ detailDialog.data.residentAddress }}</el-descriptions-item>
          <el-descriptions-item label="访问目的">{{ detailDialog.data.purpose }}</el-descriptions-item>
          <el-descriptions-item label="来访时间">{{ detailDialog.data.visitTime }}</el-descriptions-item>
          <el-descriptions-item label="停留时长">
            {{ detailDialog.data.stayDuration }}{{ detailDialog.data.timeUnit === 'hours' ? '小时' : detailDialog.data.timeUnit }}
          </el-descriptions-item>
          <el-descriptions-item label="访客状态">
            <el-tag :type="getStatusType(detailDialog.data.status)" size="small">
              {{ getStatusLabel(detailDialog.data.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="是否常用">
            <el-tag :type="detailDialog.data.isUsual ? 'success' : 'info'" size="small">
              {{ detailDialog.data.isUsual ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="核验人">{{ detailDialog.data.verifyBy || '未核验' }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ detailDialog.data.createTime }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ detailDialog.data.updateTime || '无' }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ detailDialog.data.note || '无' }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <template #footer>
        <el-button @click="detailDialog.visible = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 编辑弹窗 -->
    <visitor-edit @search="search" ref="editDialog" />
  </div>
</template>

<script>
import { listVisitor, getVisitor, deleteVisitor } from '@/api/visitor'
import { getSelectedCommunityId, hasSelectedCommunity } from '@/store/modules/options'
import VisitorEdit from '@/components/visitor/VisitorEdit.vue'

export default {
  name: 'VisitorList',
  
  components: {
    VisitorEdit
  },

  data() {
    return {
      // 搜索条件
      searchModel: {
        pageNum: 1,
        pageSize: 10,
        visitorName: '',
        phone: '',
        residentName: '',
        status: ''
      },
      // 访客列表
      visitorList: [],
      // 总数
      total: 0,
      // 加载状态
      loading: false,
      // 详情弹窗
      detailDialog: {
        visible: false,
        title: '访客详情',
        data: null
      }
    }
  },

  computed: {
    // 是否有选中的小区
    hasCurrentCommunity() {
      return hasSelectedCommunity()
    }
  },

  methods: {
    /**
     * 搜索访客列表
     */
    search() {
      this.loading = true

      // 获取当前选中的小区ID
      const communityId = getSelectedCommunityId()
      
      if (!communityId) {
        this.visitorList = []
        this.total = 0
        this.loading = false
        return
      }

      // 构建查询参数，过滤空值
      const params = { communityId }
      Object.keys(this.searchModel).forEach(key => {
        if (this.searchModel[key] !== '' && this.searchModel[key] !== null && this.searchModel[key] !== undefined) {
          params[key] = this.searchModel[key]
        }
      })

      listVisitor(params).then(res => {
        this.visitorList = res.data.data.list || []
        this.total = res.data.data.total || 0
      }).catch(err => {
        console.error('获取访客列表失败:', err)
        this.$message.error(err.data?.errorMessage || '获取访客列表失败')
      }).finally(() => {
        this.loading = false
      })
    },

    /**
     * 重置搜索条件
     */
    resetSearch() {
      this.searchModel = {
        pageNum: 1,
        pageSize: 10,
        visitorName: '',
        phone: '',
        residentName: '',
        status: ''
      }
      this.search()
    },

    /**
     * 查看详情
     */
    viewDetail(row) {
      getVisitor(row.id).then(res => {
        this.detailDialog.data = res.data.data
        this.detailDialog.visible = true
      }).catch(err => {
        console.error('获取访客详情失败:', err)
        this.$message.error(err.data?.errorMessage || '获取访客详情失败')
      })
    },

    /**
     * 添加访客
     */
    add() {
      this.$refs.editDialog.open()
    },

    /**
     * 编辑访客
     */
    edit(row) {
      this.$refs.editDialog.open(row)
    },

    /**
     * 删除访客
     */
    deleted(id) {
      this.$confirm('确定要删除这个访客记录吗？删除后无法恢复！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteVisitor(id).then(() => {
          this.search()
          this.$message.success('删除成功')
        }).catch(err => {
          console.error('删除访客失败:', err)
          this.$message.error(err.data?.errorMessage || '删除失败')
        })
      }).catch(() => {
        // 用户取消删除
      })
    },

    /**
     * 分页变化
     */
    currentChange(num) {
      this.searchModel.pageNum = num
      this.search()
    },

    /**
     * 获取状态标签
     */
    getStatusLabel(status) {
      const statusMap = {
        'pending': '待访问',
        'visited': '已访问',
        'expired': '已过期'
      }
      return statusMap[status] || status
    },

    /**
     * 获取状态类型
     */
    getStatusType(status) {
      const statusMap = {
        'pending': 'warning',
        'visited': 'success',
        'expired': 'danger'
      }
      return statusMap[status] || 'info'
    }
  },

  created() {
    // 初始化时检查是否有选中的小区
    if (this.hasCurrentCommunity) {
      this.search()
    }
  }
}
</script>

<style scoped>
.visitor-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}

.card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 16px;
  margin-bottom: 16px;
}

.card--search {
  flex-shrink: 0;
}

.card--table {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  margin-bottom: 0;
}

.search-flex {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.table-col {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.data-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100% !important;
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: center;
  flex-shrink: 0;
}

.detail-content {
  max-height: 500px;
  overflow-y: auto;
}

/* 暗色主题适配 */
.dark-theme .card {
  background: var(--card-background);
  color: var(--text-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-flex {
    flex-direction: column;
    align-items: stretch;
  }

  .search-flex > * {
    width: 100%;
    margin-right: 0 !important;
    margin-bottom: 8px;
  }

  .search-flex > *:last-child {
    margin-bottom: 0;
  }

  .el-dialog {
    width: 95% !important;
    margin: 5vh auto !important;
  }
}
</style>
