import request from '@/utils/request'

/**
 * 分页查询日志列表
 * @param {Object} data 查询参数
 * @returns {Promise}
 */
export const listLog = (data) =>
	request({
		url: '/manage-api/v1/log/page',
		method: 'get',
		params: data
	})

/**
 * 通过ID查询系统日志
 * @param {number} id 日志ID
 * @returns {Promise}
 */
export const getLog = (id) =>
	request({
		url: '/manage-api/v1/log',
		method: 'get',
		params: { id }
	})

/**
 * 删除日志
 * @param {number} id 日志ID
 * @returns {Promise}
 */
export const deleteLog = (id) =>
	request({
		url: '/manage-api/v1/log/' + id,
		method: 'delete'
	})

/**
 * 批量删除日志
 * @param {Array} ids 日志ID数组
 * @returns {Promise}
 */
export const batchDeleteLog = (ids) =>
	request({
		url: '/manage-api/v1/log/batch',
		method: 'delete',
		data: ids
	})

/**
 * 清空日志
 * @param {string} beforeDate 清空指定日期之前的日志 (YYYY-MM-DD)
 * @returns {Promise}
 */
export const clearLogs = (beforeDate) =>
	request({
		url: '/manage-api/v1/log/clear',
		method: 'delete',
		params: { beforeDate }
	})