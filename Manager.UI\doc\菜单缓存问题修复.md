# 菜单缓存问题修复

## 🔍 问题分析

用户反馈：在权限变更后，刷新页面时菜单列表仍然显示旧的权限数据，怀疑 `onLoad` 函数中的 `load-menu` 接口存在缓存问题。

### 问题原因

1. **浏览器缓存**：`load-menu` 接口可能被浏览器缓存，导致返回旧数据
2. **服务端缓存**：后端可能对菜单数据进行了缓存
3. **本地存储缓存**：前端可能缓存了权限数据

## ✅ 修复方案

### 1. API 层面防缓存

#### 修复 `loadMenu` API
```javascript
// src/api/system/menu.js
export const loadMenu = () =>
	request({
		url: '/manage-api/v1/menu/load-menu',
		method: 'get',
		params: {
			_t: Date.now() // 添加时间戳防止缓存
		},
		headers: {
			'Cache-Control': 'no-cache',
			'Pragma': 'no-cache'
		}
	})
```

#### 修复 `currentUser` API
```javascript
// src/api/system/user.js
export const currentUser = () =>
    request({
        url: '/manage-api/v1/user/current',
        method: 'get',
        params: {
            _t: Date.now() // 添加时间戳防止缓存
        },
        headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        }
    })
```

### 2. 增强调试信息

#### 优化 `onLoad` 函数日志
```javascript
// src/store/modules/user.js
async function onLoad(toPath) {
	console.log('🔄 onLoad 开始执行，toPath:', toPath, '时间戳:', new Date().toISOString())

	try {
		console.log('🌐 开始获取用户信息和权限...')
		const [user_res, permission_res] = await Promise.all([
			currentUser(),
			loadMenu()
		])
		console.log('✅ 用户信息和权限获取成功:', { 
			user: user_res.data.data, 
			permissions: permission_res.data.data,
			permissionCount: permission_res.data.data?.length || 0,
			timestamp: new Date().toISOString()
		})
		// ...
	}
}
```

### 3. 强制刷新机制

#### 新增 `forceReloadMenu` 函数
```javascript
// src/store/modules/user.js
// 强制刷新菜单和权限（清除缓存）
const forceReloadMenu = async () => {
	console.log('🔄 强制刷新菜单和权限，清除缓存...')
	
	// 清除本地存储的权限数据
	window.$local?.remove('frontPermissions')
	window.$local?.remove('treePermissions')
	
	// 重新加载
	return await onLoad(true)
}

export {
	reload,
	forceReloadMenu,  // 新增导出
	setCommunityList,
	setSelectedCommunity,
	clearCommunityData
}
```

## 🔧 使用方法

### 1. 正常刷新（现有方式）
```javascript
import { reload } from '@/store/modules/user'

// 正常重新加载用户信息和菜单
await reload()
```

### 2. 强制刷新（新增方式）
```javascript
import { forceReloadMenu } from '@/store/modules/user'

// 强制清除缓存并重新加载菜单
await forceReloadMenu()
```

### 3. 在权限管理页面中使用
```javascript
// 在角色权限修改后调用
async handlePermissionChange() {
    try {
        // 保存权限变更
        await savePermissions()
        
        // 强制刷新菜单
        await forceReloadMenu()
        
        this.$message.success('权限更新成功，菜单已刷新')
    } catch (error) {
        this.$message.error('权限更新失败')
    }
}
```

## 🔍 调试方法

### 1. 检查网络请求
打开浏览器开发者工具 → Network 标签页：
- 查看 `load-menu` 请求是否带有时间戳参数
- 检查请求头是否包含 `Cache-Control: no-cache`
- 确认每次刷新都有新的网络请求

### 2. 检查控制台日志
查看控制台输出：
```
🔄 onLoad 开始执行，toPath: xxx 时间戳: 2025-06-27T06:24:27.440Z
🌐 开始获取用户信息和权限...
✅ 用户信息和权限获取成功: { permissionCount: 15, timestamp: ... }
```

### 3. 检查本地存储
在控制台执行：
```javascript
// 查看存储的权限数据
console.log('frontPermissions:', window.$local?.get('frontPermissions'))
console.log('treePermissions:', window.$local?.get('treePermissions'))

// 手动清除缓存
window.$local?.remove('frontPermissions')
window.$local?.remove('treePermissions')
```

## 🎯 预期效果

修复后的效果：
1. **每次刷新都获取最新数据**：时间戳参数确保不使用缓存
2. **权限变更立即生效**：强制刷新机制清除所有缓存
3. **详细的调试信息**：便于排查问题
4. **灵活的刷新选项**：普通刷新和强制刷新两种方式

## 📋 测试步骤

1. **修改用户权限**：在后台修改某个用户的菜单权限
2. **刷新页面**：在前端刷新页面，检查菜单是否更新
3. **检查网络请求**：确认 `load-menu` 请求带有新的时间戳
4. **检查控制台**：查看权限数量和时间戳是否为最新
5. **强制刷新测试**：调用 `forceReloadMenu()` 确认强制刷新功能

## 🚨 注意事项

1. **时间戳参数**：确保每次请求都有唯一的时间戳
2. **缓存头设置**：`Cache-Control` 和 `Pragma` 头必须正确设置
3. **本地存储清理**：强制刷新时要清除相关的本地存储
4. **错误处理**：网络请求失败时要有适当的错误处理

现在菜单缓存问题应该得到彻底解决！
