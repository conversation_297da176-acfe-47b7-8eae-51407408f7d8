App.vue:12 初始化主题: light
user.js?t=1750770231989:229 🔍 路由守卫开始执行: /home
user.js?t=1750770231989:245 获取到的 token: 存在 string
user.js?t=1750770231989:273 有 token 但未登录，尝试加载用户信息
user.js?t=1750770231989:70 onLoad 开始执行，toPath: /home
user.js?t=1750770231989:90 从 Pinia 获取token: {hasToken: true, tokenType: 'string'}
user.js?t=1750770231989:94 开始获取用户信息和权限...
user.js?t=1750770231989:99 用户信息和权限获取成功: {user: {…}, permissions: Array(95)}
user.js?t=1750770231989:110 筛选出的菜单权限数量: 29
user.js?t=1750770231989:145 ✅ 添加路由: /community 菜单: 小区信息
user.js?t=1750770231989:145 ✅ 添加路由: /org 菜单: 组织管理
user.js?t=1750770231989:145 ✅ 添加路由: /member 菜单: 微信用户
user.js?t=1750770231989:145 ✅ 添加路由: /goodsList 菜单: 好物列表
user.js?t=1750770231989:145 ✅ 添加路由: /orderList 菜单: 订单管理
user.js?t=1750770231989:145 ✅ 添加路由: /job 菜单: 定时任务
user.js?t=1750770231989:145 ✅ 添加路由: /dept 菜单: 部门管理
user.js?t=1750770231989:145 ✅ 添加路由: /person 菜单: 员工管理
user.js?t=1750770231989:145 ✅ 添加路由: /position 菜单: 职位管理
user.js?t=1750770231989:145 ✅ 添加路由: /visiror 菜单: 小区访客
user.js?t=1750770231989:145 ✅ 添加路由: /workorder 菜单: 小区工单
user.js?t=1750770231989:124 ⚠️ 跳过无组件路径的菜单: 系统管理
onLoad @ user.js?t=1750770231989:124
await in onLoad
（匿名） @ user.js?t=1750770231989:275
（匿名） @ vue-router.mjs:2107
runWithContext @ vue-router.mjs:2074
（匿名） @ vue-router.mjs:2107
（匿名） @ vue-router.mjs:2079
runWithContext @ runtime-core.esm-bundler.js:4023
runWithContext @ vue-router.mjs:3387
（匿名） @ vue-router.mjs:3743
Promise.then
（匿名） @ vue-router.mjs:3743
runGuardQueue @ vue-router.mjs:3743
（匿名） @ vue-router.mjs:3413
Promise.then
navigate @ vue-router.mjs:3406
pushWithRedirect @ vue-router.mjs:3327
pushWithRedirect @ vue-router.mjs:3303
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
（匿名） @ main.js?t=1750770231989:46
user.js?t=1750770231989:145 ✅ 添加路由: /user 菜单: 用户管理
user.js?t=1750770231989:145 ✅ 添加路由: /building 菜单: 小区楼房
user.js?t=1750770231989:145 ✅ 添加路由: /role 菜单: 角色管理
user.js?t=1750770231989:145 ✅ 添加路由: /goods 菜单: 好物管理
user.js?t=1750770231989:145 ✅ 添加路由: /resident 菜单: 小区住户
user.js?t=1750770231989:145 ✅ 添加路由: /paymentItemsList 菜单: 缴费项目
user.js?t=1750770231989:145 ✅ 添加路由: /menu 菜单: 菜单管理
user.js?t=1750770231989:145 ✅ 添加路由: /vehicleList 菜单: 小区车辆
user.js?t=1750770231989:124 ⚠️ 跳过无组件路径的菜单: 物业缴费
onLoad @ user.js?t=1750770231989:124
await in onLoad
（匿名） @ user.js?t=1750770231989:275
（匿名） @ vue-router.mjs:2107
runWithContext @ vue-router.mjs:2074
（匿名） @ vue-router.mjs:2107
（匿名） @ vue-router.mjs:2079
runWithContext @ runtime-core.esm-bundler.js:4023
runWithContext @ vue-router.mjs:3387
（匿名） @ vue-router.mjs:3743
Promise.then
（匿名） @ vue-router.mjs:3743
runGuardQueue @ vue-router.mjs:3743
（匿名） @ vue-router.mjs:3413
Promise.then
navigate @ vue-router.mjs:3406
pushWithRedirect @ vue-router.mjs:3327
pushWithRedirect @ vue-router.mjs:3303
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
（匿名） @ main.js?t=1750770231989:46
user.js?t=1750770231989:145 ✅ 添加路由: /propertyBillList 菜单: 物业账单
user.js?t=1750770231989:145 ✅ 添加路由: /dict 菜单: 数据字典
user.js?t=1750770231989:124 ⚠️ 跳过无组件路径的菜单: 小区管理
onLoad @ user.js?t=1750770231989:124
await in onLoad
（匿名） @ user.js?t=1750770231989:275
（匿名） @ vue-router.mjs:2107
runWithContext @ vue-router.mjs:2074
（匿名） @ vue-router.mjs:2107
（匿名） @ vue-router.mjs:2079
runWithContext @ runtime-core.esm-bundler.js:4023
runWithContext @ vue-router.mjs:3387
（匿名） @ vue-router.mjs:3743
Promise.then
（匿名） @ vue-router.mjs:3743
runGuardQueue @ vue-router.mjs:3743
（匿名） @ vue-router.mjs:3413
Promise.then
navigate @ vue-router.mjs:3406
pushWithRedirect @ vue-router.mjs:3327
pushWithRedirect @ vue-router.mjs:3303
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
（匿名） @ main.js?t=1750770231989:46
user.js?t=1750770231989:124 ⚠️ 跳过无组件路径的菜单: 消息中心
onLoad @ user.js?t=1750770231989:124
await in onLoad
（匿名） @ user.js?t=1750770231989:275
（匿名） @ vue-router.mjs:2107
runWithContext @ vue-router.mjs:2074
（匿名） @ vue-router.mjs:2107
（匿名） @ vue-router.mjs:2079
runWithContext @ runtime-core.esm-bundler.js:4023
runWithContext @ vue-router.mjs:3387
（匿名） @ vue-router.mjs:3743
Promise.then
（匿名） @ vue-router.mjs:3743
runGuardQueue @ vue-router.mjs:3743
（匿名） @ vue-router.mjs:3413
Promise.then
navigate @ vue-router.mjs:3406
pushWithRedirect @ vue-router.mjs:3327
pushWithRedirect @ vue-router.mjs:3303
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
（匿名） @ main.js?t=1750770231989:46
user.js?t=1750770231989:124 ⚠️ 跳过无组件路径的菜单: 工单管理
onLoad @ user.js?t=1750770231989:124
await in onLoad
（匿名） @ user.js?t=1750770231989:275
（匿名） @ vue-router.mjs:2107
runWithContext @ vue-router.mjs:2074
（匿名） @ vue-router.mjs:2107
（匿名） @ vue-router.mjs:2079
runWithContext @ runtime-core.esm-bundler.js:4023
runWithContext @ vue-router.mjs:3387
（匿名） @ vue-router.mjs:3743
Promise.then
（匿名） @ vue-router.mjs:3743
runGuardQueue @ vue-router.mjs:3743
（匿名） @ vue-router.mjs:3413
Promise.then
navigate @ vue-router.mjs:3406
pushWithRedirect @ vue-router.mjs:3327
pushWithRedirect @ vue-router.mjs:3303
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
（匿名） @ main.js?t=1750770231989:46
user.js?t=1750770231989:145 ✅ 添加路由: /activity 菜单: 活动管理
user.js?t=1750770231989:145 ✅ 添加路由: /noticeList 菜单: 消息通知
user.js?t=1750770231989:145 ✅ 添加路由: /imagetext 菜单: 图文管理
user.js?t=1750770231989:164 📊 路由添加完成: 成功 24 个，跳过 0 个
user.js?t=1750770231989:209 构建的菜单树: (7) [{…}, {…}, {…}, {…}, {…}, {…}, {…}]
user.js?t=1750770231989:223 onLoad 执行完成，logined: true
user.js?t=1750770231989:276 用户信息加载成功，继续导航
user.js?t=1750770231989:355 🚀 应用启动检查: {hasToken: true, hasPermissions: true, hasUserInfo: true}
user.js?t=1750770231989:382 ✅ 应用启动时恢复登录状态和菜单树
index.vue:302 获取到的菜单树: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}}
error.ts:14 ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.
For more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes

    at debugWarn (error.ts:13:37)
    at watch.immediate (index.ts:6:7)
    at callWithErrorHandling (runtime-core.esm-bundler.js:199:19)
    at callWithAsyncErrorHandling (runtime-core.esm-bundler.js:206:17)
    at baseWatchOptions.call (runtime-core.esm-bundler.js:6246:47)
    at job (reactivity.esm-bundler.js:1826:18)
    at watch (reactivity.esm-bundler.js:1861:7)
    at doWatch (runtime-core.esm-bundler.js:6274:23)
    at watch2 (runtime-core.esm-bundler.js:6207:10)
    at useDeprecated (index.ts:4:3)
debugWarn @ error.ts:14
watch.immediate @ index.ts:6
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
baseWatchOptions.call @ runtime-core.esm-bundler.js:6246
job @ reactivity.esm-bundler.js:1826
watch @ reactivity.esm-bundler.js:1861
doWatch @ runtime-core.esm-bundler.js:6274
watch2 @ runtime-core.esm-bundler.js:6207
useDeprecated @ index.ts:4
useButton @ use-button.ts:11
setup @ button.vue:56
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7960
setupComponent @ runtime-core.esm-bundler.js:7921
mountComponent @ runtime-core.esm-bundler.js:5243
processComponent @ runtime-core.esm-bundler.js:5209
patch @ runtime-core.esm-bundler.js:4727
mountChildren @ runtime-core.esm-bundler.js:4959
mountElement @ runtime-core.esm-bundler.js:4882
processElement @ runtime-core.esm-bundler.js:4847
patch @ runtime-core.esm-bundler.js:4715
mountChildren @ runtime-core.esm-bundler.js:4959
processFragment @ runtime-core.esm-bundler.js:5139
patch @ runtime-core.esm-bundler.js:4701
mountChildren @ runtime-core.esm-bundler.js:4959
mountElement @ runtime-core.esm-bundler.js:4882
processElement @ runtime-core.esm-bundler.js:4847
patch @ runtime-core.esm-bundler.js:4715
componentUpdateFn @ runtime-core.esm-bundler.js:5353
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5481
mountComponent @ runtime-core.esm-bundler.js:5256
processComponent @ runtime-core.esm-bundler.js:5209
patch @ runtime-core.esm-bundler.js:4727
mountChildren @ runtime-core.esm-bundler.js:4959
processFragment @ runtime-core.esm-bundler.js:5139
patch @ runtime-core.esm-bundler.js:4701
mountChildren @ runtime-core.esm-bundler.js:4959
mountElement @ runtime-core.esm-bundler.js:4882
processElement @ runtime-core.esm-bundler.js:4847
patch @ runtime-core.esm-bundler.js:4715
componentUpdateFn @ runtime-core.esm-bundler.js:5353
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5481
mountComponent @ runtime-core.esm-bundler.js:5256
processComponent @ runtime-core.esm-bundler.js:5209
patch @ runtime-core.esm-bundler.js:4727
componentUpdateFn @ runtime-core.esm-bundler.js:5353
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5481
mountComponent @ runtime-core.esm-bundler.js:5256
processComponent @ runtime-core.esm-bundler.js:5209
patch @ runtime-core.esm-bundler.js:4727
componentUpdateFn @ runtime-core.esm-bundler.js:5433
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7374
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6250
effect2.scheduler @ reactivity.esm-bundler.js:1842
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:609
trigger @ reactivity.esm-bundler.js:583
set value @ reactivity.esm-bundler.js:1460
finalizeNavigation @ vue-router.mjs:3503
（匿名） @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
pushWithRedirect @ vue-router.mjs:3303
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
（匿名） @ main.js?t=1750770231989:46
3error.ts:14 ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.
For more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes

    at debugWarn (error.ts:13:37)
    at watch.immediate (index.ts:6:7)
    at callWithErrorHandling (runtime-core.esm-bundler.js:199:19)
    at callWithAsyncErrorHandling (runtime-core.esm-bundler.js:206:17)
    at baseWatchOptions.call (runtime-core.esm-bundler.js:6246:47)
    at job (reactivity.esm-bundler.js:1826:18)
    at watch (reactivity.esm-bundler.js:1861:7)
    at doWatch (runtime-core.esm-bundler.js:6274:23)
    at watch2 (runtime-core.esm-bundler.js:6207:10)
    at useDeprecated (index.ts:4:3)
debugWarn @ error.ts:14
watch.immediate @ index.ts:6
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
baseWatchOptions.call @ runtime-core.esm-bundler.js:6246
job @ reactivity.esm-bundler.js:1826
watch @ reactivity.esm-bundler.js:1861
doWatch @ runtime-core.esm-bundler.js:6274
watch2 @ runtime-core.esm-bundler.js:6207
useDeprecated @ index.ts:4
useButton @ use-button.ts:11
setup @ button.vue:56
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7960
setupComponent @ runtime-core.esm-bundler.js:7921
mountComponent @ runtime-core.esm-bundler.js:5243
processComponent @ runtime-core.esm-bundler.js:5209
patch @ runtime-core.esm-bundler.js:4727
mountChildren @ runtime-core.esm-bundler.js:4959
processFragment @ runtime-core.esm-bundler.js:5139
patch @ runtime-core.esm-bundler.js:4701
mountChildren @ runtime-core.esm-bundler.js:4959
processFragment @ runtime-core.esm-bundler.js:5139
patch @ runtime-core.esm-bundler.js:4701
mountChildren @ runtime-core.esm-bundler.js:4959
mountElement @ runtime-core.esm-bundler.js:4882
processElement @ runtime-core.esm-bundler.js:4847
patch @ runtime-core.esm-bundler.js:4715
mountChildren @ runtime-core.esm-bundler.js:4959
processFragment @ runtime-core.esm-bundler.js:5139
patch @ runtime-core.esm-bundler.js:4701
componentUpdateFn @ runtime-core.esm-bundler.js:5353
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5481
mountComponent @ runtime-core.esm-bundler.js:5256
processComponent @ runtime-core.esm-bundler.js:5209
patch @ runtime-core.esm-bundler.js:4727
mountChildren @ runtime-core.esm-bundler.js:4959
processFragment @ runtime-core.esm-bundler.js:5139
patch @ runtime-core.esm-bundler.js:4701
mountChildren @ runtime-core.esm-bundler.js:4959
processFragment @ runtime-core.esm-bundler.js:5139
patch @ runtime-core.esm-bundler.js:4701
componentUpdateFn @ runtime-core.esm-bundler.js:5353
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5481
mountComponent @ runtime-core.esm-bundler.js:5256
processComponent @ runtime-core.esm-bundler.js:5209
patch @ runtime-core.esm-bundler.js:4727
componentUpdateFn @ runtime-core.esm-bundler.js:5353
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5481
mountComponent @ runtime-core.esm-bundler.js:5256
processComponent @ runtime-core.esm-bundler.js:5209
patch @ runtime-core.esm-bundler.js:4727
mountChildren @ runtime-core.esm-bundler.js:4959
processFragment @ runtime-core.esm-bundler.js:5139
patch @ runtime-core.esm-bundler.js:4701
componentUpdateFn @ runtime-core.esm-bundler.js:5353
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5481
mountComponent @ runtime-core.esm-bundler.js:5256
processComponent @ runtime-core.esm-bundler.js:5209
patch @ runtime-core.esm-bundler.js:4727
componentUpdateFn @ runtime-core.esm-bundler.js:5353
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5481
mountComponent @ runtime-core.esm-bundler.js:5256
processComponent @ runtime-core.esm-bundler.js:5209
patch @ runtime-core.esm-bundler.js:4727
mountChildren @ runtime-core.esm-bundler.js:4959
processFragment @ runtime-core.esm-bundler.js:5139
patch @ runtime-core.esm-bundler.js:4701
mountChildren @ runtime-core.esm-bundler.js:4959
mountElement @ runtime-core.esm-bundler.js:4882
processElement @ runtime-core.esm-bundler.js:4847
patch @ runtime-core.esm-bundler.js:4715
componentUpdateFn @ runtime-core.esm-bundler.js:5353
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5481
mountComponent @ runtime-core.esm-bundler.js:5256
processComponent @ runtime-core.esm-bundler.js:5209
patch @ runtime-core.esm-bundler.js:4727
mountChildren @ runtime-core.esm-bundler.js:4959
processFragment @ runtime-core.esm-bundler.js:5139
patch @ runtime-core.esm-bundler.js:4701
mountChildren @ runtime-core.esm-bundler.js:4959
processFragment @ runtime-core.esm-bundler.js:5139
patch @ runtime-core.esm-bundler.js:4701
componentUpdateFn @ runtime-core.esm-bundler.js:5353
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5481
mountComponent @ runtime-core.esm-bundler.js:5256
processComponent @ runtime-core.esm-bundler.js:5209
patch @ runtime-core.esm-bundler.js:4727
mountChildren @ runtime-core.esm-bundler.js:4959
processFragment @ runtime-core.esm-bundler.js:5139
patch @ runtime-core.esm-bundler.js:4701
mountChildren @ runtime-core.esm-bundler.js:4959
processFragment @ runtime-core.esm-bundler.js:5139
patch @ runtime-core.esm-bundler.js:4701
componentUpdateFn @ runtime-core.esm-bundler.js:5353
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5481
mountComponent @ runtime-core.esm-bundler.js:5256
processComponent @ runtime-core.esm-bundler.js:5209
patch @ runtime-core.esm-bundler.js:4727
mountChildren @ runtime-core.esm-bundler.js:4959
processFragment @ runtime-core.esm-bundler.js:5139
patch @ runtime-core.esm-bundler.js:4701
componentUpdateFn @ runtime-core.esm-bundler.js:5353
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5481
mountComponent @ runtime-core.esm-bundler.js:5256
processComponent @ runtime-core.esm-bundler.js:5209
patch @ runtime-core.esm-bundler.js:4727
componentUpdateFn @ runtime-core.esm-bundler.js:5353
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5481
mountComponent @ runtime-core.esm-bundler.js:5256
processComponent @ runtime-core.esm-bundler.js:5209
patch @ runtime-core.esm-bundler.js:4727
mountChildren @ runtime-core.esm-bundler.js:4959
processFragment @ runtime-core.esm-bundler.js:5139
patch @ runtime-core.esm-bundler.js:4701
mountChildren @ runtime-core.esm-bundler.js:4959
mountElement @ runtime-core.esm-bundler.js:4882
processElement @ runtime-core.esm-bundler.js:4847
patch @ runtime-core.esm-bundler.js:4715
mountChildren @ runtime-core.esm-bundler.js:4959
mountElement @ runtime-core.esm-bundler.js:4882
processElement @ runtime-core.esm-bundler.js:4847
patch @ runtime-core.esm-bundler.js:4715
mountChildren @ runtime-core.esm-bundler.js:4959
mountElement @ runtime-core.esm-bundler.js:4882
processElement @ runtime-core.esm-bundler.js:4847
patch @ runtime-core.esm-bundler.js:4715
componentUpdateFn @ runtime-core.esm-bundler.js:5353
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5481
mountComponent @ runtime-core.esm-bundler.js:5256
processComponent @ runtime-core.esm-bundler.js:5209
patch @ runtime-core.esm-bundler.js:4727
mountChildren @ runtime-core.esm-bundler.js:4959
processFragment @ runtime-core.esm-bundler.js:5139
patch @ runtime-core.esm-bundler.js:4701
mountChildren @ runtime-core.esm-bundler.js:4959
processFragment @ runtime-core.esm-bundler.js:5139
patch @ runtime-core.esm-bundler.js:4701
mountChildren @ runtime-core.esm-bundler.js:4959
processFragment @ runtime-core.esm-bundler.js:5139
patch @ runtime-core.esm-bundler.js:4701
mountChildren @ runtime-core.esm-bundler.js:4959
processFragment @ runtime-core.esm-bundler.js:5139
patch @ runtime-core.esm-bundler.js:4701
componentUpdateFn @ runtime-core.esm-bundler.js:5353
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5481
mountComponent @ runtime-core.esm-bundler.js:5256
processComponent @ runtime-core.esm-bundler.js:5209
patch @ runtime-core.esm-bundler.js:4727
mountChildren @ runtime-core.esm-bundler.js:4959
mountElement @ runtime-core.esm-bundler.js:4882
processElement @ runtime-core.esm-bundler.js:4847
patch @ runtime-core.esm-bundler.js:4715
componentUpdateFn @ runtime-core.esm-bundler.js:5353
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5481
mountComponent @ runtime-core.esm-bundler.js:5256
processComponent @ runtime-core.esm-bundler.js:5209
patch @ runtime-core.esm-bundler.js:4727
componentUpdateFn @ runtime-core.esm-bundler.js:5353
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5481
mountComponent @ runtime-core.esm-bundler.js:5256
processComponent @ runtime-core.esm-bundler.js:5209
patch @ runtime-core.esm-bundler.js:4727
componentUpdateFn @ runtime-core.esm-bundler.js:5353
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5481
mountComponent @ runtime-core.esm-bundler.js:5256
processComponent @ runtime-core.esm-bundler.js:5209
patch @ runtime-core.esm-bundler.js:4727
mountChildren @ runtime-core.esm-bundler.js:4959
processFragment @ runtime-core.esm-bundler.js:5139
patch @ runtime-core.esm-bundler.js:4701
mountChildren @ runtime-core.esm-bundler.js:4959
mount @ runtime-core.esm-bundler.js:824
mountToTarget @ runtime-core.esm-bundler.js:846
process @ runtime-core.esm-bundler.js:868
patch @ runtime-core.esm-bundler.js:4739
componentUpdateFn @ runtime-core.esm-bundler.js:5353
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5481
mountComponent @ runtime-core.esm-bundler.js:5256
processComponent @ runtime-core.esm-bundler.js:5209
patch @ runtime-core.esm-bundler.js:4727
componentUpdateFn @ runtime-core.esm-bundler.js:5353
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5481
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7374
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6250
effect2.scheduler @ reactivity.esm-bundler.js:1842
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:609
trigger @ reactivity.esm-bundler.js:583
set value @ reactivity.esm-bundler.js:1460
finalizeNavigation @ vue-router.mjs:3503
（匿名） @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
pushWithRedirect @ vue-router.mjs:3303
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
（匿名） @ main.js?t=1750770231989:46
user.js?t=1750770231989:229 🔍 路由守卫开始执行: /member
user.js?t=1750770231989:245 获取到的 token: 存在 string
user.js?t=1750770231989:327 🚀 路由守卫通过，继续导航到: /member
userEdit.vue:420 UserEditNew 组件已挂载
22error.ts:14 ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.
For more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes

    at debugWarn (error.ts:13:37)
    at watch.immediate (index.ts:6:7)
    at callWithErrorHandling (runtime-core.esm-bundler.js:199:19)
    at callWithAsyncErrorHandling (runtime-core.esm-bundler.js:206:17)
    at baseWatchOptions.call (runtime-core.esm-bundler.js:6246:47)
    at job (reactivity.esm-bundler.js:1826:18)
    at watch (reactivity.esm-bundler.js:1861:7)
    at doWatch (runtime-core.esm-bundler.js:6274:23)
    at watch2 (runtime-core.esm-bundler.js:6207:10)
    at useDeprecated (index.ts:4:3)
debugWarn @ error.ts:14
watch.immediate @ index.ts:6
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
baseWatchOptions.call @ runtime-core.esm-bundler.js:6246
job @ reactivity.esm-bundler.js:1826
watch @ reactivity.esm-bundler.js:1861
doWatch @ runtime-core.esm-bundler.js:6274
watch2 @ runtime-core.esm-bundler.js:6207
useDeprecated @ index.ts:4
useButton @ use-button.ts:11
setup @ button.vue:56
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7960
setupComponent @ runtime-core.esm-bundler.js:7921
mountComponent @ runtime-core.esm-bundler.js:5243
processComponent @ runtime-core.esm-bundler.js:5209
patch @ runtime-core.esm-bundler.js:4727
mountChildren @ runtime-core.esm-bundler.js:4959
processFragment @ runtime-core.esm-bundler.js:5139
patch @ runtime-core.esm-bundler.js:4701
mountChildren @ runtime-core.esm-bundler.js:4959
mountElement @ runtime-core.esm-bundler.js:4882
processElement @ runtime-core.esm-bundler.js:4847
patch @ runtime-core.esm-bundler.js:4715
mountChildren @ runtime-core.esm-bundler.js:4959
processFragment @ runtime-core.esm-bundler.js:5139
patch @ runtime-core.esm-bundler.js:4701
mountChildren @ runtime-core.esm-bundler.js:4959
mountElement @ runtime-core.esm-bundler.js:4882
processElement @ runtime-core.esm-bundler.js:4847
patch @ runtime-core.esm-bundler.js:4715
componentUpdateFn @ runtime-core.esm-bundler.js:5353
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5481
mountComponent @ runtime-core.esm-bundler.js:5256
processComponent @ runtime-core.esm-bundler.js:5209
patch @ runtime-core.esm-bundler.js:4727
mountChildren @ runtime-core.esm-bundler.js:4959
mountElement @ runtime-core.esm-bundler.js:4882
processElement @ runtime-core.esm-bundler.js:4847
patch @ runtime-core.esm-bundler.js:4715
patchKeyedChildren @ runtime-core.esm-bundler.js:5666
patchChildren @ runtime-core.esm-bundler.js:5538
processFragment @ runtime-core.esm-bundler.js:5183
patch @ runtime-core.esm-bundler.js:4701
patchKeyedChildren @ runtime-core.esm-bundler.js:5624
patchChildren @ runtime-core.esm-bundler.js:5538
patchElement @ runtime-core.esm-bundler.js:5012
processElement @ runtime-core.esm-bundler.js:4858
patch @ runtime-core.esm-bundler.js:4715
componentUpdateFn @ runtime-core.esm-bundler.js:5433
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5475
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
trigger @ reactivity.esm-bundler.js:736
set @ reactivity.esm-bundler.js:1019
set @ runtime-core.esm-bundler.js:3170
init @ miniUserList.vue:280
await in init
created @ miniUserList.vue:291
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:217
callHook @ runtime-core.esm-bundler.js:3652
applyOptions @ runtime-core.esm-bundler.js:3570
finishComponentSetup @ runtime-core.esm-bundler.js:8073
setupStatefulComponent @ runtime-core.esm-bundler.js:7996
setupComponent @ runtime-core.esm-bundler.js:7921
mountComponent @ runtime-core.esm-bundler.js:5243
processComponent @ runtime-core.esm-bundler.js:5209
patch @ runtime-core.esm-bundler.js:4727
componentUpdateFn @ runtime-core.esm-bundler.js:5433
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7374
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6250
effect2.scheduler @ reactivity.esm-bundler.js:1842
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:609
trigger @ reactivity.esm-bundler.js:583
set value @ reactivity.esm-bundler.js:1460
finalizeNavigation @ vue-router.mjs:3503
（匿名） @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
handleMenuItemClick @ menu.ts:163
handleClick @ menu-item.vue:84
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
invoker @ runtime-dom.esm-bundler.js:729
userEdit.vue:398 角色列表API响应: {data: {…}, status: 200, statusText: '', headers: {…}, config: {…}, …}
userEdit.vue:409 处理后的角色列表数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}}
userEdit.vue:354 组织API响应: {data: {…}, status: 200, statusText: '', headers: {…}, config: {…}, …}
user.js?t=1750770231989:229 🔍 路由守卫开始执行: /job
user.js?t=1750770231989:245 获取到的 token: 存在 string
user.js?t=1750770231989:327 🚀 路由守卫通过，继续导航到: /job
10error.ts:14 ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.
For more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes

    at debugWarn (error.ts:13:37)
    at watch.immediate (index.ts:6:7)
    at callWithErrorHandling (runtime-core.esm-bundler.js:199:19)
    at callWithAsyncErrorHandling (runtime-core.esm-bundler.js:206:17)
    at baseWatchOptions.call (runtime-core.esm-bundler.js:6246:47)
    at job (reactivity.esm-bundler.js:1826:18)
    at watch (reactivity.esm-bundler.js:1861:7)
    at doWatch (runtime-core.esm-bundler.js:6274:23)
    at watch2 (runtime-core.esm-bundler.js:6207:10)
    at useDeprecated (index.ts:4:3)
debugWarn @ error.ts:14
watch.immediate @ index.ts:6
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
baseWatchOptions.call @ runtime-core.esm-bundler.js:6246
job @ reactivity.esm-bundler.js:1826
watch @ reactivity.esm-bundler.js:1861
doWatch @ runtime-core.esm-bundler.js:6274
watch2 @ runtime-core.esm-bundler.js:6207
useDeprecated @ index.ts:4
useButton @ use-button.ts:11
setup @ button.vue:56
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7960
setupComponent @ runtime-core.esm-bundler.js:7921
mountComponent @ runtime-core.esm-bundler.js:5243
processComponent @ runtime-core.esm-bundler.js:5209
patch @ runtime-core.esm-bundler.js:4727
mountChildren @ runtime-core.esm-bundler.js:4959
processFragment @ runtime-core.esm-bundler.js:5139
patch @ runtime-core.esm-bundler.js:4701
mountChildren @ runtime-core.esm-bundler.js:4959
mountElement @ runtime-core.esm-bundler.js:4882
processElement @ runtime-core.esm-bundler.js:4847
patch @ runtime-core.esm-bundler.js:4715
mountChildren @ runtime-core.esm-bundler.js:4959
processFragment @ runtime-core.esm-bundler.js:5139
patch @ runtime-core.esm-bundler.js:4701
mountChildren @ runtime-core.esm-bundler.js:4959
mountElement @ runtime-core.esm-bundler.js:4882
processElement @ runtime-core.esm-bundler.js:4847
patch @ runtime-core.esm-bundler.js:4715
componentUpdateFn @ runtime-core.esm-bundler.js:5353
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5481
mountComponent @ runtime-core.esm-bundler.js:5256
processComponent @ runtime-core.esm-bundler.js:5209
patch @ runtime-core.esm-bundler.js:4727
mountChildren @ runtime-core.esm-bundler.js:4959
mountElement @ runtime-core.esm-bundler.js:4882
processElement @ runtime-core.esm-bundler.js:4847
patch @ runtime-core.esm-bundler.js:4715
patchKeyedChildren @ runtime-core.esm-bundler.js:5666
patchChildren @ runtime-core.esm-bundler.js:5538
processFragment @ runtime-core.esm-bundler.js:5183
patch @ runtime-core.esm-bundler.js:4701
patchKeyedChildren @ runtime-core.esm-bundler.js:5624
patchChildren @ runtime-core.esm-bundler.js:5538
patchElement @ runtime-core.esm-bundler.js:5012
processElement @ runtime-core.esm-bundler.js:4858
patch @ runtime-core.esm-bundler.js:4715
componentUpdateFn @ runtime-core.esm-bundler.js:5433
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5475
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
trigger @ reactivity.esm-bundler.js:736
set @ reactivity.esm-bundler.js:1019
set @ runtime-core.esm-bundler.js:3170
（匿名） @ list.vue:240
Promise.then
search @ list.vue:239
created @ list.vue:189
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:217
callHook @ runtime-core.esm-bundler.js:3652
applyOptions @ runtime-core.esm-bundler.js:3570
finishComponentSetup @ runtime-core.esm-bundler.js:8073
setupStatefulComponent @ runtime-core.esm-bundler.js:7996
setupComponent @ runtime-core.esm-bundler.js:7921
mountComponent @ runtime-core.esm-bundler.js:5243
processComponent @ runtime-core.esm-bundler.js:5209
patch @ runtime-core.esm-bundler.js:4727
componentUpdateFn @ runtime-core.esm-bundler.js:5433
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7374
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6250
effect2.scheduler @ reactivity.esm-bundler.js:1842
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:609
trigger @ reactivity.esm-bundler.js:583
set value @ reactivity.esm-bundler.js:1460
finalizeNavigation @ vue-router.mjs:3503
（匿名） @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
handleMenuItemClick @ menu.ts:163
handleClick @ menu-item.vue:84
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
invoker @ runtime-dom.esm-bundler.js:729
user.js?t=1750770231989:229 🔍 路由守卫开始执行: /dept
user.js?t=1750770231989:245 获取到的 token: 存在 string
user.js?t=1750770231989:327 🚀 路由守卫通过，继续导航到: /dept


上面是刷新页面之前,下面是刷新页面之后的





runtime-core.esm-bundler.js:4634 Feature flag __VUE_PROD_HYDRATION_MISMATCH_DETAILS__ is not explicitly defined. You are running the esm-bundler build of Vue, which expects these compile-time feature flags to be globally injected via the bundler config in order to get better tree-shaking in the production bundle.

For more details, see https://link.vuejs.org/feature-flags.
vue-router.mjs:51 [Vue Router warn]: No match found for location with path "/dept"
App.vue:12 初始化主题: light
user.js?t=1750770231989:229 🔍 路由守卫开始执行: /dept
user.js?t=1750770231989:245 获取到的 token: 存在 string
user.js?t=1750770231989:273 有 token 但未登录，尝试加载用户信息
user.js?t=1750770231989:70 onLoad 开始执行，toPath: /dept
user.js?t=1750770231989:90 从 Pinia 获取token: 
{hasToken: true, tokenType: 'string'}
user.js?t=1750770231989:94 开始获取用户信息和权限...
user.js?t=1750770231989:355 🚀 应用启动检查: 
{hasToken: true, hasPermissions: true, hasUserInfo: true}
user.js?t=1750770231989:382 ✅ 应用启动时恢复登录状态和菜单树
user.js?t=1750770231989:99 用户信息和权限获取成功: 
{user: {…}, permissions: Array(95)}
user.js?t=1750770231989:110 筛选出的菜单权限数量: 29
user.js?t=1750770231989:145 ✅ 添加路由: /community 菜单: 小区信息
user.js?t=1750770231989:145 ✅ 添加路由: /org 菜单: 组织管理
user.js?t=1750770231989:145 ✅ 添加路由: /member 菜单: 微信用户
user.js?t=1750770231989:145 ✅ 添加路由: /goodsList 菜单: 好物列表
user.js?t=1750770231989:145 ✅ 添加路由: /orderList 菜单: 订单管理
user.js?t=1750770231989:145 ✅ 添加路由: /job 菜单: 定时任务
user.js?t=1750770231989:145 ✅ 添加路由: /dept 菜单: 部门管理
user.js?t=1750770231989:145 ✅ 添加路由: /person 菜单: 员工管理
user.js?t=1750770231989:145 ✅ 添加路由: /position 菜单: 职位管理
user.js?t=1750770231989:145 ✅ 添加路由: /visiror 菜单: 小区访客
user.js?t=1750770231989:145 ✅ 添加路由: /workorder 菜单: 小区工单
user.js?t=1750770231989:124 ⚠️ 跳过无组件路径的菜单: 系统管理
user.js?t=1750770231989:145 ✅ 添加路由: /user 菜单: 用户管理
user.js?t=1750770231989:145 ✅ 添加路由: /building 菜单: 小区楼房
user.js?t=1750770231989:145 ✅ 添加路由: /role 菜单: 角色管理
user.js?t=1750770231989:145 ✅ 添加路由: /goods 菜单: 好物管理
user.js?t=1750770231989:145 ✅ 添加路由: /resident 菜单: 小区住户
user.js?t=1750770231989:145 ✅ 添加路由: /paymentItemsList 菜单: 缴费项目
user.js?t=1750770231989:145 ✅ 添加路由: /menu 菜单: 菜单管理
user.js?t=1750770231989:145 ✅ 添加路由: /vehicleList 菜单: 小区车辆
user.js?t=1750770231989:124 ⚠️ 跳过无组件路径的菜单: 物业缴费
user.js?t=1750770231989:145 ✅ 添加路由: /propertyBillList 菜单: 物业账单
user.js?t=1750770231989:145 ✅ 添加路由: /dict 菜单: 数据字典
user.js?t=1750770231989:124 ⚠️ 跳过无组件路径的菜单: 小区管理
user.js?t=1750770231989:124 ⚠️ 跳过无组件路径的菜单: 消息中心
user.js?t=1750770231989:124 ⚠️ 跳过无组件路径的菜单: 工单管理
user.js?t=1750770231989:145 ✅ 添加路由: /activity 菜单: 活动管理
user.js?t=1750770231989:145 ✅ 添加路由: /noticeList 菜单: 消息通知
user.js?t=1750770231989:145 ✅ 添加路由: /imagetext 菜单: 图文管理
user.js?t=1750770231989:164 📊 路由添加完成: 成功 24 个，跳过 0 个
user.js?t=1750770231989:209 构建的菜单树: 
(7) [{…}, {…}, {…}, {…}, {…}, {…}, {…}]
user.js?t=1750770231989:223 onLoad 执行完成，logined: true
user.js?t=1750770231989:276 用户信息加载成功，继续导航
﻿

