<template>
  <el-dialog
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    title="选择住户"
    width="800px"
    :close-on-click-modal="false"
  >
    <!-- 搜索区域 -->
    <div class="search-area">
      <el-input
        v-model="searchKeyword"
        placeholder="输入住户姓名或手机号搜索"
        clearable
        style="width: 300px; margin-right: 16px;"
        @input="handleSearch"
      />
      <el-button type="primary" @click="search">搜索</el-button>
    </div>

    <!-- 住户列表 -->
    <div class="resident-list">
      <el-table
        :data="residentList"
        v-loading="loading"
        @row-click="handleRowClick"
        highlight-current-row
        style="width: 100%"
        max-height="400px"
      >
        <el-table-column prop="name" label="姓名" width="120" align="center"/>
        <el-table-column prop="phone" label="手机号" width="140" align="center"/>
        <el-table-column prop="address" label="住址" align="center" show-overflow-tooltip/>
        <el-table-column prop="roomNumber" label="房号" width="120" align="center"/>
        <el-table-column label="操作" width="100" align="center">
          <template #default="scope">
            <el-button type="text" size="small" @click="selectResident(scope.row)">
              选择
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-area" v-if="total > 0">
      <el-pagination
        @current-change="currentChange"
        :current-page="pageNum"
        :page-size="pageSize"
        layout="total, prev, pager, next"
        :total="total"
        small
      />
    </div>

    <template #footer>
      <el-button @click="$emit('update:modelValue', false)">取消</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { getSelectedCommunityId } from '@/store/modules/options'

export default {
  name: 'ResidentSelectDialog',

  props: {
    modelValue: {
      type: Boolean,
      default: false
    }
  },

  emits: ['update:modelValue', 'confirm'],

  data() {
    return {
      searchKeyword: '',
      residentList: [],
      loading: false,
      pageNum: 1,
      pageSize: 10,
      total: 0
    }
  },

  watch: {
    modelValue(val) {
      if (val) {
        this.resetData()
        this.search()
      }
    }
  },

  methods: {
    /**
     * 重置数据
     */
    resetData() {
      this.searchKeyword = ''
      this.residentList = []
      this.pageNum = 1
      this.total = 0
    },

    /**
     * 搜索住户
     */
    search() {
      this.loading = true

      const communityId = getSelectedCommunityId()
      if (!communityId) {
        this.loading = false
        this.$message.warning('请先选择小区')
        return
      }

      // 模拟API调用 - 实际项目中需要替换为真实的住户查询API
      const params = {
        communityId,
        pageNum: this.pageNum,
        pageSize: this.pageSize
      }

      if (this.searchKeyword) {
        params.keyword = this.searchKeyword
      }

      // 这里应该调用真实的住户查询API
      // 暂时使用模拟数据
      setTimeout(() => {
        this.residentList = [
          {
            id: 1,
            name: '张三',
            phone: '13800138001',
            address: '1号楼1单元101室',
            roomNumber: '101'
          },
          {
            id: 2,
            name: '李四',
            phone: '13800138002',
            address: '1号楼1单元102室',
            roomNumber: '102'
          },
          {
            id: 3,
            name: '王五',
            phone: '13800138003',
            address: '2号楼2单元201室',
            roomNumber: '201'
          }
        ].filter(item => {
          if (!this.searchKeyword) return true
          return item.name.includes(this.searchKeyword) || 
                 item.phone.includes(this.searchKeyword)
        })
        
        this.total = this.residentList.length
        this.loading = false
      }, 500)

      // 真实API调用示例：
      // import { listResident } from '@/api/resident'
      // listResident(params).then(res => {
      //   this.residentList = res.data.data.list || []
      //   this.total = res.data.data.total || 0
      // }).catch(err => {
      //   console.error('获取住户列表失败:', err)
      //   this.$message.error('获取住户列表失败')
      // }).finally(() => {
      //   this.loading = false
      // })
    },

    /**
     * 处理搜索输入
     */
    handleSearch() {
      // 防抖处理
      clearTimeout(this.searchTimer)
      this.searchTimer = setTimeout(() => {
        this.pageNum = 1
        this.search()
      }, 300)
    },

    /**
     * 行点击事件
     */
    handleRowClick(row) {
      this.selectResident(row)
    },

    /**
     * 选择住户
     */
    selectResident(resident) {
      this.$emit('confirm', resident)
      this.$emit('update:modelValue', false)
    },

    /**
     * 分页变化
     */
    currentChange(num) {
      this.pageNum = num
      this.search()
    }
  },

  beforeUnmount() {
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
    }
  }
}
</script>

<style scoped>
.search-area {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #eee;
}

.resident-list {
  margin-bottom: 16px;
}

.pagination-area {
  display: flex;
  justify-content: center;
  padding-top: 16px;
  border-top: 1px solid #eee;
}

/* 表格行悬停效果 */
.el-table tbody tr:hover {
  cursor: pointer;
}

/* 暗色主题适配 */
.dark-theme .search-area,
.dark-theme .pagination-area {
  border-color: #444;
}
</style>
