# Pinia 存储迁移总结

## 📋 迁移概述

将项目中的 localStorage 存储全部迁移到 Pinia 统一管理，实现更好的状态管理和数据持久化。

## ✅ 已完成的迁移

### 🔧 1. Pinia Store 扩展

**文件**: `src/store/lcAssign.js`

**新增字段**:
```javascript
state: () => ({
  // 原有字段
  smartPropertyUserInfo: null,
  smartPropertyToken: null,
  frontPermissions: null,
  dictList: null,
  
  // 新增字段
  communityList: [],        // 小区列表
  selectedCommunity: null,  // 选中的小区
  userName: null,           // 用户名
  theme: 'light'           // 主题
})
```

**新增方法**:
```javascript
// 专用设置方法
setCommunityList(communityList)
setSelectedCommunity(selectedCommunity)
setUserName(userName)
setTheme(theme)

// 通用方法
set(key, value)  // 通用设置
get(key)         // 通用获取
has(key)         // 检查是否存在
```

**持久化配置**:
```javascript
persist: {
  paths: [ 
    'smartPropertyUserInfo',
    'smartPropertyToken',  
    'frontPermissions', 
    'dictList',
    'communityList',      // 新增
    'selectedCommunity',  // 新增
    'userName',           // 新增
    'theme'              // 新增
  ]
}
```

### 🔧 2. 登录页面迁移

**文件**: `src/views/login/login.vue`

**修改前**:
```javascript
localStorage.setItem('userName', this.dataModel.userName)
localStorage.setItem("smart_property_token"+this.dataModel.userName, JSON.stringify(tokenData))
```

**修改后**:
```javascript
window.$local.setUserName(this.dataModel.userName)
window.$local.setSmartPropertyToken(JSON.stringify(tokenData))
```

### 🔧 3. 请求工具迁移

**文件**: `src/utils/request.js`

**Token 获取**:
```javascript
// 修改前
function getToken() {
    return localStorage.getItem("smart_property_token"+localStorage.getItem("userName"))
}

// 修改后
function getToken() {
    const userName = window.$local?.get('userName')
    if (!userName) return null
    return window.$local?.get('smartPropertyToken')
}
```

**Token 更新**:
```javascript
// 修改前
localStorage.setItem("smart_property_token" + userName, JSON.stringify(currentToken))

// 修改后
window.$local?.setSmartPropertyToken(JSON.stringify(currentToken))
```

**重新登录**:
```javascript
// 修改前
function reLogin() {
    localStorage.clear()
    router.replace({ path: '/login' })
}

// 修改后
function reLogin() {
    window.$local?.removeAll()
    router.replace({ path: '/login' })
}
```

### 🔧 4. 用户模块迁移

**文件**: `src/store/modules/user.js`

**Token 检查**:
```javascript
// 修改前
var token = localStorage.getItem("smart_property_token"+localStorage.getItem("userName"))

// 修改后
const userName = window.$local?.get('userName')
var token = userName ? window.$local?.get('smartPropertyToken') : null
```

**路由守卫**:
```javascript
// 修改前
const token = localStorage.getItem("smart_property_token"+localStorage.getItem("userName"))

// 修改后
const userName = window.$local?.get('userName')
const token = userName ? window.$local?.get('smartPropertyToken') : null
```

**清除数据**:
```javascript
// 修改前
localStorage.clear()

// 修改后
window.$local?.removeAll()
```

### 🔧 5. 小区选项模块迁移

**文件**: `src/store/modules/options.js`

**核心改动**:
- 移除 localStorage 直接操作
- 通过 `window.$local` 访问 Pinia store
- 保持原有 API 接口不变，确保兼容性

**主要方法更新**:
```javascript
// 获取选中小区
export const getSelectedCommunity = () => {
  const store = getStore()
  return store?.get('selectedCommunity') || null
}

// 设置选中小区
export const setSelectedCommunity = (community) => {
  const store = getStore()
  if (store) {
    store.setSelectedCommunity(community)
  }
  // 通知监听器...
}

// 设置小区列表
export const setCommunityList = (list) => {
  const store = getStore()
  if (store) {
    store.setCommunityList(list || [])
  }
  // 自动选择逻辑...
}
```

### 🔧 6. 主题模块迁移

**文件**: `src/store/modules/theme.js`

**主要改动**:
```javascript
// 修改前
const theme = {
  currentTheme: localStorage.getItem(THEME_CONFIG.STORAGE_KEY) || THEME_CONFIG.TYPES.LIGHT,
}

// 修改后
const theme = {
  get currentTheme() {
    const store = getStore()
    return store?.get('theme') || THEME_CONFIG.TYPES.LIGHT
  },
  
  set currentTheme(value) {
    const store = getStore()
    if (store) {
      store.setTheme(value)
    }
  },
}
```

**存储操作**:
```javascript
// 修改前
localStorage.setItem(THEME_CONFIG.STORAGE_KEY, this.currentTheme)

// 修改后
const store = getStore()
if (store) {
  store.setTheme(this.currentTheme)
}
```

### 🔧 7. 布局组件迁移

**文件**: `src/layout/index.vue`

**退出登录**:
```javascript
// 修改前
localStorage.clear()

// 修改后
window.$local?.removeAll()
```

## 🎯 迁移优势

### ✅ 统一管理
- 所有状态数据集中在 Pinia store 中
- 统一的 API 接口 (`window.$local.set/get`)
- 避免数据分散在不同存储位置

### ✅ 自动持久化
- 使用 `pinia-plugin-persistedstate` 插件
- 自动同步到 localStorage
- 页面刷新后数据自动恢复

### ✅ 类型安全
- Pinia 提供更好的 TypeScript 支持
- 状态变更可追踪
- 开发工具支持更好

### ✅ 响应式
- 状态变更自动触发组件更新
- 支持计算属性和监听器
- 更好的性能优化

## 📊 使用方式

### 在 Vue 组件中
```javascript
// 方式1：使用全局 $local
this.$local.set('userName', 'test')
const userName = this.$local.get('userName')

// 方式2：使用 window.$local
window.$local.set('theme', 'dark')
const theme = window.$local.get('theme')

// 方式3：直接使用 Pinia store
import { useStore } from '@/store/lcAssign'
const store = useStore()
store.setUserName('test')
const userName = store.userName
```

### 在普通 JS 文件中
```javascript
// 使用 window.$local
window.$local.set('smartPropertyToken', token)
const token = window.$local.get('smartPropertyToken')

// 检查是否存在
if (window.$local.has('userName')) {
  // 处理逻辑
}

// 清除所有数据
window.$local.removeAll()
```

## ✅ 兼容性保证

- 原有的 API 接口保持不变
- 组件无需修改调用方式
- 渐进式迁移，向后兼容
- 保持原有的监听器机制

## 🔧 8. 组件中 $store 使用迁移

### 布局组件 (`src/layout/index.vue`)
```javascript
// 修改前
username() {
  return this.$store.user?.username || 'system';
}

// 修改后
username() {
  const userName = window.$local?.get('userName')
  if (userName) return userName

  const userInfo = this.$store.user?.userinfo
  return userInfo?.username || userInfo?.userName || 'system';
}
```

### 主题切换组件 (`src/components/ThemeSwitcher.vue`)
```javascript
// 修改前
currentTheme() {
  return this.$store.theme.currentTheme;
}

// 修改后
currentTheme() {
  return window.$local?.get('theme') || this.$store.theme?.currentTheme || 'light';
}
```

### App.vue 主题初始化
```javascript
// 修改前
if (this.$store && this.$store.theme) {
  this.$store.theme.applyTheme();
}

// 修改后
const currentTheme = window.$local?.get('theme') || 'light';
this.applyTheme(currentTheme);
```

## 🧪 测试验证

### 测试页面
创建了 `src/views/test/storageTest.vue` 测试页面，可以：
- 查看所有 Pinia 存储状态
- 测试设置用户名、Token、主题
- 验证数据持久化
- 清空所有数据

### 登录测试
1. 打开浏览器开发者工具
2. 进入登录页面
3. 输入用户名密码登录
4. 查看控制台日志，确认：
   - "登录成功，开始存储token..."
   - "Pinia 存储完成，验证结果:"
   - 存储的用户名和 token 信息

### 存储验证
在浏览器控制台执行：
```javascript
// 检查 Pinia store 是否可用
console.log('Pinia store:', window.$local)

// 检查存储的数据
console.log('用户名:', window.$local?.get('userName'))
console.log('Token:', !!window.$local?.get('smartPropertyToken'))
console.log('主题:', window.$local?.get('theme'))

// 测试设置数据
window.$local?.setUserName('test')
window.$local?.setTheme('dark')
```

## 🚨 故障排除

### 1. 登录后没有跳转
**原因**: `window.$local` 未初始化或 token 存储失败
**解决**:
- 检查控制台是否有 "Pinia store not available" 错误
- 确认 main.js 中 Pinia 初始化顺序
- 查看登录页面控制台日志

### 2. 页面刷新后数据丢失
**原因**: 持久化插件未正确配置
**解决**:
- 检查 `pinia-plugin-persistedstate` 是否正确安装
- 确认 `persist.paths` 配置包含所需字段

### 3. 主题切换不生效
**原因**: DOM 类名未正确应用
**解决**:
- 检查 `applyTheme` 方法是否正确执行
- 确认 CSS 类 `.dark-theme` 是否存在

## 🎉 迁移完成

✅ 所有 localStorage 操作已成功迁移到 Pinia 统一管理
✅ 所有 $store 使用已更新为兼容 Pinia 的方式
✅ 添加了完整的兼容性处理和错误恢复机制
✅ 创建了测试页面用于验证功能
✅ 项目现在使用统一的状态管理方案！
