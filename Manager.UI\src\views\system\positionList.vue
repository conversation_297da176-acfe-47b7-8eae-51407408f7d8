<template>
	<div class="position-list-container">
		<position-edit ref="positionEdit" :certificateList="certificateList" :statusList="statusList" @search="search"></position-edit>
		<div class="card card--search search-flex">
			<el-input v-model="searchModel.positionName" placeholder="职位" clearable style="width: 200px; margin-right: 16px;" />
			<el-button type="primary" @click="search" style="margin-right: 8px;">搜索</el-button>
			<el-button type="primary" @click="add">添加</el-button>
		</div>
		<div class="card card--table">
			<div class="table-col">
				<el-table stripe :data="positionList" style="width: 100%; height: 100%;" class="data-table">
					<el-table-column prop="id" align="center" label="ID" width="80" />
					<el-table-column prop="positionName" align="center" label="职位" min-width="200" show-overflow-tooltip />

					<el-table-column prop="createTime" align="center" label="创建时间" min-width="180" show-overflow-tooltip />
					<el-table-column align="center" label="操作" width="150" fixed="right">
						<template #default="scope">
							<el-button type="text" size="small" @click="edit(scope.row.id)">编辑</el-button>
							<el-button type="text" size="small" @click="deleted(scope.row.id)">删除</el-button>
						</template>
					</el-table-column>
				</el-table>
			</div>
			<div class="pagination-container">
				<el-pagination v-model:current-page="searchModel.pageNum" v-model:page-size="searchModel.pageSize"
					:page-sizes="[10, 20, 50, 100]" :total="total" layout="total, sizes, prev, pager, next, jumper"
					@size-change="search" @current-change="currentChange" />
			</div>
		</div>
	</div>
</template>
<script>
import { listPosition, deletePosition, getPosition } from "@/api/system/position"
import { listDictByNameEn } from "@/api/system/dict"
import mitt from "@/utils/mitt"
import positionEdit from "@/components/system/positionEdit.vue"
export default {
	components: { 
        positionEdit
      },
	data() {
		return {
			searchModel: {
				pageNum: 1,
				pageSize: 10,
				positionName: '',
				phone: '',
				positionNumber: '',
				status: ''
			},
			positionList: [],
            certificateList: [],
			statusList: [],
			total: 0
		}
	},
	methods: {
		search() {
			listPosition(this.searchModel)
				.then(res => {
					this.positionList = res.data.data.list
					this.total = res.data.data.total
				})
				.catch(err => {
					this.$message.error(err.data.errorMessage)
				})
		},
		add() {
			mitt.emit('openPositionAdd');
		},
		edit(id) {
			getPosition(id)
				.then(res => {
					mitt.emit('openPositionEdit', res.data.data)
				})
				.catch(err => {
					this.$message.error(err.data.errorMessage)
				})
		},
		deleted(id) {
			this.$confirm('删除员工, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				deletePosition(id)
					.then(() => {
						this.search()
						this.$message.success("操作成功")
					})
					.catch(err => {
						this.$message.error(err.data.errorMessage)
					})
			}).catch(() => { })
		},
		currentChange(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		formatStatus(status) {
			const statusItem = this.statusList.find(item => item.nameEn === status)
			return statusItem ? statusItem.nameCn : status
		},
		getStatusType(status) {
			// 根据状态返回对应的标签类型
			const statusMap = {
				'normal': 'success',
				'inactive': 'danger',
				'trial': 'warning'
			}
			return statusMap[status] || 'info'
		}
	
	},
	created() {
	   this.search()
	},
	unmounted() {
		mitt.off('openPositionAdd')
		mitt.off('openPositionEdit')
	}
}
</script>

<style scoped>
.position-list-container {
	display: flex;
	flex-direction: column;
	height: 100%;
	box-sizing: border-box;
}

.card--table {
	background-color: #fff;
	border-radius: 5px;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	flex: 1;
	height: 100%;
	display: flex;
	flex-direction: column;
	overflow: auto;
	margin-top: 0;
}

.table-col {
	flex: 1;
	display: flex;
	flex-direction: column;
	min-height: 0;
}

.data-table {
	flex: 1;
	display: flex;
	flex-direction: column;
	height: 100% !important;
}

.pagination-col {
	display: flex;
	justify-content: flex-end;
	margin-top: 10px;
}

.pagination-container {
	display: flex;
	justify-content: flex-end;
	margin-top: 10px;
	padding: 0 20px 20px;
}

.search-flex {
	display: flex;
	align-items: center;
}

.card--search {
	margin-bottom: 20px;
	flex: none;
	height: auto;
	padding: 20px 20px;
	display: flex;
	align-items: center;
}
</style>