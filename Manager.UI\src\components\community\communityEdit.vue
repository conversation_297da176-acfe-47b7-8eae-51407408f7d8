<template>
  <el-dialog :title="dialog.title" v-model="dialog.show" width="600px" class="community-edit-dialog">
    <el-form :model="communityModel" :rules="rules" ref="formRef" label-width="100px" class="community-edit-form">
      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="小区名称" prop="communityName">
            <el-input v-model="communityModel.communityName" placeholder="请输入小区名称" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="地址" prop="address">
            <el-input v-model="communityModel.address" placeholder="请输入地址" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="经度" prop="lng">
            <el-input v-model="communityModel.lng" :precision="6" style="width: 100%;" placeholder="经度" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="纬度" prop="lat">
            <el-input v-model="communityModel.lat" :precision="6" style="width: 100%;" placeholder="纬度" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="序号" prop="sort">
            <el-input-number v-model="communityModel.sort" :min="0" style="width: 100%;" />
          </el-form-item>
        </el-col>
    
        <el-col :span="24">
          <el-form-item label="备注" prop="note">
            <el-input v-model="communityModel.note" type="textarea" :rows="3" placeholder="请输入备注" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="扩展数据" prop="expandData">
            <el-input v-model="communityModel.expandData" type="textarea" :rows="2" placeholder="请输入扩展数据(JSON格式)" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialog.show = false">取消</el-button>
      <el-button type="primary" @click="submit">保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addCommunity, editCommunity } from '@/api/community/community'
import mitt from '@/utils/mitt'

export default {
  name: 'communityEdit',
  data() {
    return {
      communityModel: {
        id: undefined,
        communityName: '',
        address: '',
        lng: null,
        lat: null,
        sort: 0,
        orgId: null,
        note: '',
        expandData: ''
      },
      dialog: {
        show: false,
        title: ''
      },
      rules: {
        communityName: [
          { required: true, message: '请输入小区名称', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    resetForm() {
      this.$refs.formRef && this.$refs.formRef.resetFields()
      this.communityModel = {
        id: undefined,
        communityName: '',
        address: '',
        lng: null,
        lat: null,
        sort: 0,
        orgId: null,
        note: '',
        expandData: ''
      }
    },
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        const api = this.communityModel.id ? editCommunity : addCommunity
        api(this.communityModel).then(() => {
          this.$message.success('保存成功')
          this.dialog.show = false
          this.$emit('search')
        }).catch(err => {
          this.$message.error(err.data?.errorMessage || '保存失败')
        })
      })
    }
  },
  mounted() {
    mitt.on('openCommunityEdit', (data) => {
      this.resetForm()
      if (data && data.id) {
        this.communityModel = { ...data }
        this.dialog.title = '编辑小区信息'
      } else {
        this.dialog.title = '新增小区信息'
      }
      this.dialog.show = true
    })
  },
  beforeDestroy() {
    mitt.off('openCommunityEdit')
  }
}
</script>

<style scoped>
.community-edit-dialog >>> .el-dialog__body {
  padding-top: 10px;
  padding-bottom: 0;
}
.community-edit-form {
  padding: 0 10px;
}
.dialog-footer {
  padding: 10px 24px 18px 0;
  text-align: right;
}
</style> 