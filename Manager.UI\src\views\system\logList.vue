<template>
  <div class="log-list-container">
    <div class="log-list-content">
      <!-- 搜索区域 -->
      <div class="search-flex">
        <el-input
          v-model="searchModel.requestUrl"
          placeholder="请求URL"
          clearable
          style="width: 200px; margin-right: 16px;"
        />
        <el-input
          v-model="searchModel.userName"
          placeholder="用户名"
          clearable
          style="width: 150px; margin-right: 16px;"
        />
        <el-select
          v-model="searchModel.requestMethod"
          placeholder="请求方法"
          clearable
          style="width: 120px; margin-right: 16px;"
        >
          <el-option label="GET" value="GET" />
          <el-option label="POST" value="POST" />
          <el-option label="PUT" value="PUT" />
          <el-option label="DELETE" value="DELETE" />
        </el-select>
        <el-input
          v-model="searchModel.operationType"
          placeholder="操作类型"
          clearable
          style="width: 120px; margin-right: 16px;"
        />
        <el-date-picker
          v-model="dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 350px; margin-right: 16px;"
          @change="handleDateChange"
        />
        <el-button type="primary" @click="search" style="margin-right: 8px;">搜索</el-button>
        <el-button @click="resetSearch">重置</el-button>
      </div>

      <!-- 表格区域 -->
      <div class="card card--table">
        <div class="table-col">
          <el-table
            :data="logList"
            row-key="id"
            style="width: 100%; height: 100%;"
            class="data-table"
            v-loading="loading"
          >
            <el-table-column prop="id" label="日志ID" width="80" align="center"/>
            <el-table-column prop="requestMethod" label="请求方法" width="100" align="center">
              <template #default="scope">
                <el-tag :type="getMethodType(scope.row.requestMethod)" size="small">
                  {{ scope.row.requestMethod }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="requestUrl" label="请求URL" min-width="200" align="left" show-overflow-tooltip/>
            <el-table-column prop="responseCode" label="响应码" width="100" align="center">
              <template #default="scope">
                <el-tag :type="getResponseType(scope.row.responseCode)" size="small">
                  {{ scope.row.responseCode }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="userName" label="用户名" width="120" align="center" show-overflow-tooltip/>
            <el-table-column prop="ipAddress" label="IP地址" width="140" align="center"/>
            <el-table-column prop="consumeTime" label="耗时(ms)" width="100" align="center">
              <template #default="scope">
                <span :class="getTimeClass(scope.row.consumeTime)">{{ scope.row.consumeTime }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="operationType" label="操作类型" width="120" align="center" show-overflow-tooltip/>
            <el-table-column prop="createTime" label="创建时间" width="160" align="center"/>
            <el-table-column label="操作" width="120" align="center" fixed="right">
              <template #default="scope">
                <el-button type="text" @click="viewDetail(scope.row)">查看</el-button>
                <el-button type="text" @click="deleteLogItem(scope.row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            @current-change="currentChange"
            @size-change="sizeChange"
            :current-page="searchModel.pageNum"
            :page-size="searchModel.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          />
        </div>
      </div>
    </div>

    <!-- 详情弹窗 -->
    <el-dialog
      v-model="detailDialog.visible"
      :title="detailDialog.title"
      width="900px"
      :close-on-click-modal="false"
    >
      <div v-if="detailDialog.data" class="detail-content">
        <!-- 基本信息 -->
        <el-descriptions title="基本信息" :column="2" border>
          <el-descriptions-item label="日志ID">{{ detailDialog.data.id }}</el-descriptions-item>
          <el-descriptions-item label="请求方法">
            <el-tag :type="getMethodType(detailDialog.data.requestMethod)" size="small">
              {{ detailDialog.data.requestMethod }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="请求URL" :span="2">{{ detailDialog.data.requestUrl }}</el-descriptions-item>
          <el-descriptions-item label="响应码">
            <el-tag :type="getResponseType(detailDialog.data.responseCode)" size="small">
              {{ detailDialog.data.responseCode }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="耗时">
            <span :class="getTimeClass(detailDialog.data.consumeTime)">{{ detailDialog.data.consumeTime }}ms</span>
          </el-descriptions-item>
          <el-descriptions-item label="用户名">{{ detailDialog.data.userName || '-' }}</el-descriptions-item>
          <el-descriptions-item label="用户ID">{{ detailDialog.data.userId || '-' }}</el-descriptions-item>
          <el-descriptions-item label="组织ID">{{ detailDialog.data.orgId || '-' }}</el-descriptions-item>
          <el-descriptions-item label="IP地址">{{ detailDialog.data.ipAddress || '-' }}</el-descriptions-item>
          <el-descriptions-item label="操作类型">{{ detailDialog.data.operationType || '-' }}</el-descriptions-item>
          <el-descriptions-item label="操作描述">{{ detailDialog.data.operationDesc || '-' }}</el-descriptions-item>
          <el-descriptions-item label="目标ID">{{ detailDialog.data.targetId || '-' }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ detailDialog.data.createTime }}</el-descriptions-item>
        </el-descriptions>

        <!-- 请求体 -->
        <div class="detail-section" v-if="detailDialog.data.requestBody">
          <h4>请求体</h4>
          <el-input
            type="textarea"
            :rows="6"
            :value="formatJson(detailDialog.data.requestBody)"
            readonly
            class="json-textarea"
          />
        </div>

        <!-- 错误信息 -->
        <div class="detail-section" v-if="detailDialog.data.errorMessage || detailDialog.data.errorCode">
          <h4>错误信息</h4>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="错误码" v-if="detailDialog.data.errorCode">
              {{ detailDialog.data.errorCode }}
            </el-descriptions-item>
            <el-descriptions-item label="错误信息" v-if="detailDialog.data.errorMessage">
              {{ detailDialog.data.errorMessage }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 额外数据 -->
        <div class="detail-section" v-if="detailDialog.data.extraData">
          <h4>额外数据</h4>
          <el-input
            type="textarea"
            :rows="4"
            :value="formatJson(detailDialog.data.extraData)"
            readonly
            class="json-textarea"
          />
        </div>
      </div>

      <template #footer>
        <el-button @click="detailDialog.visible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { listLog, getLog, deleteLog } from '@/api/system/log'

export default {
  name: 'LogList',

  data() {
    return {
      // 搜索条件
      searchModel: {
        pageNum: 1,
        pageSize: 10,
        requestUrl: '',
        requestMethod: '',
        userName: '',
        operationType: '',
        startTime: '',
        endTime: ''
      },
      // 日期范围
      dateRange: [],
      // 日志列表
      logList: [],
      // 总数
      total: 0,
      // 加载状态
      loading: false,
      // 详情弹窗
      detailDialog: {
        visible: false,
        title: '日志详情',
        data: null
      }
    }
  },

  methods: {
    /**
     * 搜索日志列表
     */
    search() {
      this.loading = true

      // 构建查询参数，过滤空值
      const params = {}
      Object.keys(this.searchModel).forEach(key => {
        if (this.searchModel[key] !== '' && this.searchModel[key] !== null && this.searchModel[key] !== undefined) {
          params[key] = this.searchModel[key]
        }
      })

      listLog(params).then(res => {
        this.logList = res.data.data.list || []
        this.total = res.data.data.total || 0
      }).catch(err => {
        console.error('获取日志列表失败:', err)
        this.$message.error(err.data?.errorMessage || '获取日志列表失败')
      }).finally(() => {
        this.loading = false
      })
    },

    /**
     * 重置搜索条件
     */
    resetSearch() {
      this.searchModel = {
        pageNum: 1,
        pageSize: 20,
        requestUrl: '',
        requestMethod: '',
        userName: '',
        operationType: '',
        startTime: '',
        endTime: ''
      }
      this.dateRange = []
      this.search()
    },

    /**
     * 处理日期范围变化
     */
    handleDateChange(dates) {
      if (dates && dates.length === 2) {
        this.searchModel.startTime = dates[0]
        this.searchModel.endTime = dates[1]
      } else {
        this.searchModel.startTime = ''
        this.searchModel.endTime = ''
      }
    },

    /**
     * 查看详情
     */
    viewDetail(row) {
      getLog(row.id).then(res => {
        this.detailDialog.data = res.data.data
        this.detailDialog.visible = true
      }).catch(err => {
        console.error('获取日志详情失败:', err)
        this.$message.error(err.data?.errorMessage || '获取日志详情失败')
      })
    },

    /**
     * 删除日志
     */
    deleteLogItem(id) {
      this.$confirm('确定要删除这条日志吗？删除后无法恢复！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteLog(id).then(() => {
          this.search()
          this.$message.success('删除成功')
        }).catch(err => {
          console.error('删除日志失败:', err)
          this.$message.error(err.data?.errorMessage || '删除失败')
        })
      }).catch(() => {
        // 用户取消删除
      })
    },



    /**
     * 分页变化
     */
    currentChange(num) {
      this.searchModel.pageNum = num
      this.search()
    },

    /**
     * 每页大小变化
     */
    sizeChange(size) {
      this.searchModel.pageSize = size
      this.searchModel.pageNum = 1
      this.search()
    },

    /**
     * 获取请求方法类型
     */
    getMethodType(method) {
      const typeMap = {
        'GET': 'success',
        'POST': 'primary',
        'PUT': 'warning',
        'DELETE': 'danger'
      }
      return typeMap[method] || 'info'
    },

    /**
     * 获取响应码类型
     */
    getResponseType(code) {
      if (code >= 200 && code < 300) return 'success'
      if (code >= 300 && code < 400) return 'warning'
      if (code >= 400 && code < 500) return 'danger'
      if (code >= 500) return 'danger'
      return 'info'
    },

    /**
     * 获取耗时样式类
     */
    getTimeClass(time) {
      if (time < 100) return 'time-fast'
      if (time < 500) return 'time-normal'
      if (time < 1000) return 'time-slow'
      return 'time-very-slow'
    },

    /**
     * 格式化JSON
     */
    formatJson(str) {
      try {
        const obj = JSON.parse(str)
        return JSON.stringify(obj, null, 2)
      } catch (e) {
        return str
      }
    }
  },

  created() {
    this.search()
  }
}
</script>

<style scoped>
.log-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}

.log-list-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0;
}

.card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 16px;
}



.card--table {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.search-flex {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
  background: transparent;
  padding: 16px 0;
  margin-bottom: 16px;
  flex-shrink: 0;
}

.table-col {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.data-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100% !important;
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
  flex-shrink: 0;
}

.detail-content {
  max-height: 600px;
  overflow-y: auto;
}

.detail-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.detail-section h4 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 14px;
}

.json-textarea {
  font-family: 'Courier New', monospace;
}

/* 耗时样式 */
.time-fast {
  color: #67c23a;
  font-weight: bold;
}

.time-normal {
  color: #409eff;
  font-weight: bold;
}

.time-slow {
  color: #e6a23c;
  font-weight: bold;
}

.time-very-slow {
  color: #f56c6c;
  font-weight: bold;
}

/* 暗色主题适配 */
.dark-theme .card {
  background: var(--card-background);
  color: var(--text-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-flex {
    flex-direction: column;
    align-items: stretch;
  }

  .search-flex > * {
    width: 100%;
    margin-right: 0 !important;
    margin-bottom: 8px;
  }

  .search-flex > *:last-child {
    margin-bottom: 0;
  }

  .pagination-container {
    justify-content: center;
  }

  .el-dialog {
    width: 95% !important;
    margin: 5vh auto !important;
  }
}
</style>
